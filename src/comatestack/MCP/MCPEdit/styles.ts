import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {Form} from 'antd';
import bg from '@/assets/mcp/pageBg.png';
import vipbg from '@/assets/mcp/pageVipBg.png';

export const FormWrapper = styled(Form)<{official?: boolean}>`
    height: calc(100vh - 48px);
    width: 100%;
    display: flex;
    flex-direction: column;
    background: url(${({official}) => (official ? vipbg : bg)}) no-repeat;
`;

export const Content = styled.div`
    margin: 0 24px !important;
    padding: 16px 0;
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    border-radius: 6px;
`;

export const HeaderWrapper = styled.div`
    padding: 24px 24px 0px;
`;

export const tabCss = css`
    margin: 2px 0;
`;

export const errorRowCss = css`
    td {
        &: first-child {
            border-left: 2px solid #e62c4b !important;
        }
    }
`;
