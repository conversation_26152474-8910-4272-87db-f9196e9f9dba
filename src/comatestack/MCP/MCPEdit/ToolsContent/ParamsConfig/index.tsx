/* eslint-disable max-lines */
import {Checkbox, Flex, Form, TableColumnsType, Tabs, Typography} from 'antd';
import {Button} from '@panda-design/components';
import {useCallback, useEffect, useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {keys} from 'lodash';
import {IconDownSolid} from '@baidu/ee-icon';
import {Path} from '@panda-design/path-form';
import {IconSubtract} from '@/icons/mcp';
import {Gap} from '@/design/iplayground/Gap';
import {IconPlus} from '@/icons/lucide';
import {BaseParam} from '@/types/mcp/mcp';
import {StyledTable} from '../ParamList';
import {ApiParamsValidationError, useActiveTool} from '../hooks';
import {getParamValueFieldPath, useToolParamsConfigContext} from '../Provider/toolParamsConfigProvider';
import {useMCPEditFormValidationListener} from '../../Providers/MCPEditFormValidationInteractionProvider';
import ParamValueField from './ParamValueField';

const StyledTabs = styled(Tabs)`
    .ant-5-tabs-tab-active{
        .ant-5-tabs-tab-btn{
            color: #0083FF !important;
        }
    }
    .ant-5-tabs-tab{
        font-size: 14px;
        &:not(:first-child) {
            margin-left: 24px;
        }
    }
`;
const ItemWrapper = styled.div<{activeKey: string, deep?: number}>`
    position: relative;
    padding-left: ${props => (
        props.activeKey === 'body'
            ? 16 + (props.deep + 1) * 12 + 'px'
            : '42px'
    )};
    .button-wrapper {
        position: absolute;
        top: -4px;
        left: -8px;
    }
`;

const StyledTriangle = styled(IconDownSolid) <{expanded?: boolean}>`
    color: #BFBFBF;
    position: absolute;
    left: 32px;
    top: ${props => (props.expanded ? 'calc(50% - 4px)' : 'calc(50% - 6px)')};
    z-index: 10;
    margin-right: 8px;
    cursor: pointer;
    transform: ${props => (props.expanded ? 'rotate(0deg)' : 'rotate(-90deg)')};
`;

/**
 * 参数path是格式化数据源的时候生成的，在transformBodySchemaToTableTreeData函数中。
 */
export const transformPathInBody = (path: Path): Path => {
    return path.reduce(
        (acc, cur, index) => {
            return [
                ...acc,
                ...index > 0 ? ['children', cur] : [cur],
            ];
        }, []
    );
};


export const isParamRelated = (key: string, paramsList: BaseParam[]) => {
    return paramsList?.some(
        item => item.refParam === key
    );
};

interface NameFieldProps{
    name: string;
    record: BaseParam;
    index: number;
    activeKey: string;
    sourcePath: Array<string|number>;
}

const ParamNameField = ({name, record, activeKey, sourcePath}: NameFieldProps) => {
    const {getToolApiConfigData, addToolParam, removeToolParam} = useToolParamsConfigContext();
    const {activeToolIndex} = useActiveTool();
    const paramListPath = useMemo(
        () => ['tools', activeToolIndex, 'toolParams', 'toolParams'],
        [activeToolIndex]
    );
    const paramsList: BaseParam[] = Form.useWatch(paramListPath);
    const valueFieldPath = useMemo(
        () => {
            const allParams = getToolApiConfigData(activeToolIndex);
            return getParamValueFieldPath({
                basePath: [...sourcePath, activeKey],
                record,
                allParams,
            });
        },
        [getToolApiConfigData, activeToolIndex, sourcePath, activeKey, record]
    );
    const handleRelate = useCallback(
        (record: BaseParam) => {
            addToolParam(activeToolIndex, record);
        },
        [addToolParam, activeToolIndex]
    );
    const handleUnRelate = useCallback(
        (record: BaseParam) => {
            removeToolParam(activeToolIndex, record.key);
        },
        [removeToolParam, activeToolIndex]
    );

    const value = Form.useWatch(valueFieldPath);
    const canRelate = useCallback(
        (record: BaseParam) => {
            // 有值不能关联，用户的任何输入，都默认是该参数的常量值
            return record.canRelate && !value;
        },
        [value]
    );
    return (
        <ItemWrapper activeKey={activeKey} deep={record?.fieldPath?.length}>
            {
                isParamRelated(record.key, paramsList)
                    ? (
                        <span className="button-wrapper">
                            <Button
                                onClick={() => handleUnRelate(record)}
                                type="text"
                                tooltip="取消关联"
                                icon={<IconSubtract />}
                            />
                        </span>
                    )
                // object不能给tool添加参数
                    : canRelate(record) && (
                        <span className="button-wrapper">
                            <Button
                                onClick={() => handleRelate(record)}
                                type="text"
                                tooltip="关联"
                                icon={<IconPlus />}
                            />
                        </span>
                    )
            }
            {name}
        </ItemWrapper>
    );
};

const extractKeyPath = (key: string): string[] => {
    const keys = key.split('.');
    // 最后一项是校验的目标，不需要展开
    const path = keys.slice(0, -1).map((_, i) => {
        const pre = [];
        for (let j = 0; j < i + 1; j++) {
            pre.push(keys[j]);
        }
        return pre.join('.');
    });
    return path;
};

// ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters'],
const ParamsConfig = () => {
    const form = Form.useFormInstance();
    const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
    const {subscribeValidationError} = useMCPEditFormValidationListener();
    const [showRequired, setShowRequired] = useState(false);
    // path、query、cookie、body等，source对象下的key
    const [activeKey, setActiveKey] = useState<string>();
    const {activeToolIndex} = useActiveTool();
    const sourcePath = useMemo(
        () => ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters'],
        [activeToolIndex]
    );
    const source = Form.useWatch(sourcePath);
    const tabItems = useMemo(
        () => {
            const sourceKeys = keys(source);
            const items = sourceKeys.map(key => ({
                label: key,
                value: key,
                key,
            }));
            return items;
        },
        [source]
    );
    useEffect(
        () => {
            if (!activeKey) {
                setActiveKey(tabItems?.[0]?.key);
            }
        },
        [activeKey, tabItems]
    );

    const expand = useCallback(
        (keys: string[]) => {
            setExpandedRowKeys(prevKeys => {
                const newKeys = [...prevKeys];
                keys.forEach(key => {
                    if (!newKeys.includes(key)) {
                        newKeys.push(key);
                    }
                });
                return newKeys;
            });
        },
        [setExpandedRowKeys]
    );

    const unExpand = useCallback(
        (keys: string[]) => {
            setExpandedRowKeys(prevKeys => {
                const set = new Set(prevKeys);
                keys.forEach(key => {
                    if (set.has(key)) {
                        set.delete(key);
                    }
                });
                return [...set.values()];
            });
        },
        [setExpandedRowKeys]
    );

    const onExpand = useCallback(
        (expanded: boolean, record: BaseParam) => {
            const key = record.key;
            if (expanded) {
                expand([key]);
            } else {
                unExpand([key]);
            }
        }
        ,
        [expand, unExpand]
    );

    const handleValidationError = useCallback(
        (e: ApiParamsValidationError) => {
            const key = e.errorParam.key.split('.').shift();
            if (key === 'body') {
                const keys = extractKeyPath(e.errorParam.key);
                // 第一项是body、header、cookie这样的tab key，不需要
                expand(keys.slice(1));
            }
            setActiveKey(key);
            setTimeout(() => {
                window.console.log('validation');
                form.validateFields();
            }, 500);
        },
        [expand, form]
    );

    useEffect(
        () => {
            const {unsubscribe} = subscribeValidationError(handleValidationError);
            return unsubscribe;
        },
        [handleValidationError, subscribeValidationError]
    );

    const columns = useMemo<TableColumnsType<BaseParam>>(
        () => [
            {
                title: '参数名',
                dataIndex: 'name',
                width: 200,
                render: (name: string, record, index) => (
                    <ParamNameField
                        name={name}
                        record={record}
                        index={index}
                        activeKey={activeKey}
                        sourcePath={sourcePath}
                    />
                ),
            },
            {
                title: '类型',
                width: 60,
                dataIndex: 'type',
            },
            {
                title: '是否必须',
                width: 60,
                dataIndex: 'required',
                render: (required: boolean) => (required ? '是' : '否'),
            },
            {
                title: '说明',
                width: 150,
                dataIndex: 'description',
                render: description => (
                    <Typography.Paragraph ellipsis={{rows: 2, tooltip: {title: description}}}>
                        {description}
                    </Typography.Paragraph>
                ),
            },
            {
                title: '默认值',
                dataIndex: 'value',
                width: 150,
                render: (_, record) => {
                    return (
                        <ParamValueField
                            basePath={[...sourcePath, activeKey]}
                            record={record}
                        />
                    );
                },
            },
        ],
        [activeKey, sourcePath]
    );
    return (
        <Flex vertical>
            <Flex justify="space-between">
                <StyledTabs activeKey={activeKey} onChange={setActiveKey} items={tabItems} />
                <Checkbox
                    checked={showRequired}
                    onChange={e => setShowRequired(e.target.checked)}
                    style={{alignItems: 'center'}}
                >
                    仅看必填项
                </Checkbox>
            </Flex>
            <Gap />
            <Form.Item name={sourcePath} style={{marginBottom: 0}}>
                <StyledTable
                    rowKey="key"
                    expandable={{
                        defaultExpandAllRows: true,
                        expandedRowKeys: expandedRowKeys,
                        expandIcon: ({expanded, onExpand, record}) => {
                            if (record?.children) {
                                return (
                                    <span onClick={e => onExpand(record, e)}>
                                        <StyledTriangle expanded={expanded} />
                                    </span>
                                );
                            }
                        },
                        onExpand: onExpand,
                    }}
                    dataSource={source?.[activeKey] || []}
                    columns={columns}
                    pagination={{hideOnSinglePage: true}}
                />
            </Form.Item>
        </Flex>
    );
};

export default ParamsConfig;

