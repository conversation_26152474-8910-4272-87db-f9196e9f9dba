/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {useBoolean} from 'huse';
import {useCallback, useEffect, useState} from 'react';
import {Form} from 'antd';
import {IconPlus} from '@/icons/lucide';
import {BaseParam} from '@/types/mcp/mcp';
import {CustomTreeSelect} from '../CustomTreeSelect';
import {useActiveTool, useHandleRelateParamTreeData} from '../hooks';
import {TreeData} from '../../utils';
import {useToolParamsConfigContext} from '../Provider/toolParamsConfigProvider';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    position: relative;
    &:hover{
        color: #317ff5 !important;
    }
`;

const StyledCustomTreeSelect = styled(CustomTreeSelect)`
    .ant-5-select-selector {
        position: absolute !important;
        left: 0;
        z-index: -1;
    }
`;

const RelateAPIRequestButton = () => {
    const {batchSetToolParams} = useToolParamsConfigContext();
    const [open, {on, off}] = useBoolean();
    const [selectedTrees, setSelectedTrees] = useState<string[]>([]);
    const {activeToolIndex} = useActiveTool();
    const params: BaseParam[] = Form.useWatch(['tools', activeToolIndex, 'toolParams', 'toolParams']);
    // eslint-disable-next-line max-len
    const treeData = useHandleRelateParamTreeData({canSelectRoot: true, titleWidthRoot: false});
    const handleChange = useCallback(
        (values: string[]) => {
            batchSetToolParams(activeToolIndex, values);
        },
        [activeToolIndex, batchSetToolParams]
    );

    useEffect(
        () => {
            setSelectedTrees(params?.map(item => item?.refParam));
        },
        [params]
    );
    useEffect(
        () => {
            if (!params && treeData?.length > 0) {
                // 默认选中所有的参数
                const values = treeData.reduce(
                    (acc, cur) => {
                        return [...acc, ...cur.key === 'body' ? [] : cur.children?.map((item: TreeData) => item.value)];
                    },
                    [] as string[]
                );
                handleChange(values);
            }
        },
        [handleChange, params, treeData]
    );

    useEffect(
        () => {
            window.addEventListener('click', off);
        },
        [off]
    );
    return (
        <StyledButton
            icon={<IconPlus />}
            onClick={e => {
                e.stopPropagation();
                on();
            }}
            type="text"
        >
            关联API请求参数
            <StyledCustomTreeSelect
                multiple
                treeCheckable
                onChange={handleChange}
                treeData={treeData}
                open={open}
                noStyle
                value={selectedTrees}
            />
        </StyledButton>
    );
};

export default RelateAPIRequestButton;

