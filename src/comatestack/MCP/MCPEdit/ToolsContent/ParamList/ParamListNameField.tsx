import {Button} from '@panda-design/components';
import {Path} from '@panda-design/path-form';
import {Form, Input, InputProps, Space} from 'antd';
import {useCallback} from 'react';
import {IconSubtract} from '@/icons/mcp';
import {useActiveTool} from '../hooks';
import {useToolParamsConfigContext} from '../Provider/toolParamsConfigProvider';

interface CustomInputProps extends InputProps {
    onChange?: (value?: any) => void;
    path: Path;
}

const NameInput = ({onChange, path, ...props}: CustomInputProps) => {
    const {syncParamNameFieldValueToConfigTable} = useToolParamsConfigContext();
    const recordRefParam = Form.useWatch([...path, 'refParam']);
    const {activeToolIndex} = useActiveTool();
    const handelChange = useCallback(
        (e: any) => {
            const value = e.target.value;
            onChange?.(value);
            syncParamNameFieldValueToConfigTable(activeToolIndex, recordRefParam ?? '', value);
        },
        [activeToolIndex, onChange, recordRefParam, syncParamNameFieldValueToConfigTable]
    );
    return (
        <Input
            {...props}
            onChange={handelChange}
        />
    );
};

interface Props {
    path: Path;
}

const ParamListNameField = ({path}: Props) => {
    const {removeToolParam} = useToolParamsConfigContext();
    const recordRefParam = Form.useWatch([...path, 'refParam']);
    const {activeToolIndex} = useActiveTool();
    const handleRemove = useCallback(
        () => {
            removeToolParam(activeToolIndex, recordRefParam);
        },
        [activeToolIndex, recordRefParam, removeToolParam]
    );
    return (
        <Space>
            <Button icon={<IconSubtract />} onClick={handleRemove} tooltip="移除" type="text" />
            <Form.Item style={{marginBottom: 0}} name={[...path, 'name']}>
                <NameInput path={path} placeholder="请输入参数名称" />
            </Form.Item>
        </Space>
    );
};

export default ParamListNameField;

