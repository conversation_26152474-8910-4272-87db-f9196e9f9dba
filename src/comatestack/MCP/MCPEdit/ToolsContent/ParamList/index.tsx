/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Button, Modal} from '@panda-design/components';
import {Flex, Form, Input, Space, Table, TableColumnsType} from 'antd';
import {Path} from '@panda-design/path-form';
import {useCallback, useMemo} from 'react';
import {Gap} from '@/design/iplayground/Gap';
import {BaseParam} from '@/types/mcp/mcp';
import {IconAlert} from '@/icons/mcp';
import {useToolParamsConfigContext} from '../Provider/toolParamsConfigProvider';
import {useActiveTool} from '../hooks';
import ParamListNameField from './ParamListNameField';
import RelateAPIRequestButton from './RelateAPIRequestButton';
import ParamDescription<PERSON>ield from './ParamDescriptionField';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    position: relative;
    &:hover{
        color: #317ff5 !important;
    }
`;

const Line = styled.div`
    width: 1px;
    height: 14px;
    background-color: #d8d8d8;
`;

export const StyledTable = styled(Table<any>)`
    .ant-5-table-thead{
        .ant-5-table-cell{
            &:nth-child(1){
                padding-left: 48px;
            }
        }
    }
`;

export const RequiredTitle = styled.div`
    &:before{
        content: '*';
        color: red;
        margin-right: 4px;
    }
`;


interface TableProps {
    value?: BaseParam[];
    path: Path;
}
const CustomTable = ({value = [], path}: TableProps) => {
    const columns = useMemo<TableColumnsType<BaseParam>>(
        () => [
            {
                title: <RequiredTitle>参数名称</RequiredTitle>,
                dataIndex: 'name',
                fixed: 'left',
                width: '220px',
                render: (_, record, index) => {
                    return (
                        <ParamListNameField path={[...path, index]} />
                    );
                },
            },
            {
                title: <RequiredTitle>描述</RequiredTitle>,
                dataIndex: 'description',
                width: '220px',
                render: (_, record, index) => (
                    <ParamDescriptionField path={[...path, index]} />
                ),
            },
            {
                title: <RequiredTitle>关联参数</RequiredTitle>,
                dataIndex: 'refParam',
                width: '200px',
            },
            {
                title: '类型',
                dataIndex: 'dataType',
                width: '80px',
                render: (dataType, record) => {
                    return (
                        dataType || record.type
                    );
                },
            },
            {
                title: '是否必须',
                dataIndex: 'required',
                render: (required: boolean) => (required ? '是' : '否'),
            },
            {
                title: <RequiredTitle>示例</RequiredTitle>,
                dataIndex: 'exampleValue',
                width: '200px',
                render: (_, record, index) => {
                    // TODO 所有传复杂path的地方，都应该调整，不要让UI层纠结怎么传path
                    return (
                        <Form.Item
                            style={{marginBottom: 0}}
                            name={[...path, index, 'exampleValue']}
                            rules={[{required: true, message: '请输入示例'}]}
                        >
                            <Input placeholder="请输入示例" />
                        </Form.Item>
                    );
                },
            },
        ],
        [path]
    );
    return (
        <StyledTable
            id="toolParamsTable"
            style={{width: '1000px'}}
            dataSource={value}
            columns={columns}
            pagination={false}
            rowClassName="toolParamsTableRow"
            scroll={{x: 'max-content'}}
            rowKey="refParam"
        />
    );

};

interface Props {
    path: Path;
}

const ParamsList = ({path}: Props) => {
    const {clearToolParams} = useToolParamsConfigContext();
    const {activeToolIndex} = useActiveTool();
    const handleClear = useCallback(
        () => {
            Modal.confirm({
                content: '确定清空参数列表吗？',
                icon: <IconAlert />,
                onOk: () => {
                    clearToolParams(activeToolIndex);
                },
            });
        },
        [activeToolIndex, clearToolParams]
    );

    return (
        <>
            <Flex vertical>
                <Space size={4}>
                    <RelateAPIRequestButton />
                    <Line />
                    <StyledButton type="text" onClick={handleClear}>清空</StyledButton>
                </Space>
                <Gap />
                <Form.Item name={path}>
                    <CustomTable path={path} />
                </Form.Item>
            </Flex>
        </>
    );
};

export default ParamsList;
