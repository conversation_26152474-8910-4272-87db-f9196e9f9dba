/* eslint-disable max-len */
import {Form} from 'antd';
import {useCallback} from 'react';
import {v4} from 'uuid';
import {ApiDefinition, MCPToolItem} from '@/types/mcp/mcp';
import {useMCPServer} from '@/regions/mcp/mcpServer';
import {useMCPServerId} from '@/components/MCP/hooks';
import {transformBodySchemaToTableTreeData, transformRequestParamsToTableData} from '../../utils';
import {useToolParamsConfigContext} from '../Provider/toolParamsConfigProvider';

export const useAddTool = () => {
    const {setFieldValue} = Form.useFormInstance();
    const tools: MCPToolItem[] = Form.useWatch('tools');
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const {setToolApiConfigData} = useToolParamsConfigContext();
    const addTool = useCallback(
        (value: any) => {
            if (value.apiInfos) {
                setFieldValue('tools', [
                    ...(tools || []),
                    ...value.apiInfos.map((apiInfo: ApiDefinition, index: number) => {
                        const {name, description, parameters, requestBody, path, method, responses, responseExamples} = apiInfo;
                        const jsonSchema = requestBody?.jsonSchema;
                        const newParameters = {
                            ...transformRequestParamsToTableData(parameters),
                            body: jsonSchema ? transformBodySchemaToTableTreeData(jsonSchema) : undefined,
                        };
                        const newIndex = tools?.length ? tools.length + index : index;
                        setToolApiConfigData(newIndex, newParameters);
                        return {
                            name,
                            description,
                            toolKey: v4(),
                            toolConf: {
                                openapiConf: {
                                    name,
                                    description,
                                    method,
                                    path,
                                    requestBody: requestBody,
                                    parameters: newParameters,
                                    responses,
                                    responseExamples,
                                },
                            },
                            toolParams: {
                                serverParams: mcpServer?.serverParams,
                                // TODO 必须传个空数组，不然会被错误赋值一次，很奇怪，找不到赋值的地方
                                // @ts-ignore
                                toolParams: [],
                            },
                        };
                    }),
                ]);
            } else {
                setFieldValue('tools', [...(tools || []), value]);
            }
        },
        [mcpServer?.serverParams, setFieldValue, setToolApiConfigData, tools]
    );
    return addTool;
};
