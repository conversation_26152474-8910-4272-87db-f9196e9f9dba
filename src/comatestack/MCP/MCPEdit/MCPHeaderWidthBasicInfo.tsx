import {Divider, Flex, Space, Typography} from 'antd';
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {IconList} from '@/icons/mcp';
import {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';
import {useMCPServerId, useMCPWorkspaceId} from '@/components/MCP/hooks';
import {useMCPServer} from '@/regions/mcp/mcpServer';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import {MCPSpaceLink} from '@/links/mcp';
import EllipsisButton from '@/components/MCP/EllipsisButton';
import GoBackButton from '@/components/MCP/GoBackButton';
import UpdateInfo from '@/components/MCP/UpdateInfo';
import SendButton from './SendButton';
import MoveButton from './MoveButton';
import DeleteButton from './DeleteButton';
import RepealButton from './RepealButton';

const Wrapper = styled(Flex)`
    width: 100%;
    margin-bottom: 16px;
    overflow: hidden;
`;

const MCPHeaderWidthBasicInfo = () => {
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const spaceId = useMCPWorkspaceId();

    if (!mcpServer) {
        return null;
    }

    return (
        <Wrapper justify="space-between" align="center">
            <Space size={8}>
                <GoBackButton size={24} url={MCPSpaceLink.toUrl({workspaceId: spaceId})} />
                <Space size={16}>
                    <MCPServerAvatar size={32} icon={mcpServer?.icon} radius={4} />
                    <Typography.Title level={4} ellipsis>{mcpServer?.name}</Typography.Title>
                    <MCPReleaseStatus
                        status={mcpServer.serverStatus || 'draft'}
                        publishType={mcpServer.serverPublishType}
                    />
                </Space>
            </Space>
            <Flex align="center" gap={8} style={{overflow: 'hidden'}}>
                <UpdateInfo username={mcpServer?.lastModifyUser} time={mcpServer?.lastModifyTime} />
                <Divider type="vertical" style={{borderColor: '#D9D9D9'}} />
                <Flex align="center" gap={8}>
                    <Button
                        icon={<IconList />}
                        type="text"
                        disabled
                        tooltip="即将上线"
                    >
                        历史
                    </Button>
                    <RepealButton />
                    <SendButton />
                    <EllipsisButton>
                        <MoveButton />
                        <DeleteButton />
                    </EllipsisButton>
                </Flex>
            </Flex>
        </Wrapper>
    );
};
export default MCPHeaderWidthBasicInfo;
