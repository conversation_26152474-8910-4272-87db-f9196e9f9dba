import {Path} from '@panda-design/path-form';
import {Form, Input} from 'antd';
import {useAuthDescription} from './hooks/useAuthDescription';

interface AuthDescriptionProps {
    path?: Path;
}

interface ChangeEvent {
    target: {
        value: string;
    };
}

const AuthDescriptionField = ({path = []}: AuthDescriptionProps) => {
    const {authType, fieldPaths, formConfig, handleChange} = useAuthDescription(path);

    const handleInputChange = (e: ChangeEvent) => {
        handleChange(e.target.value);
    };

    if (authType === 'NONE') {
        return null;
    }

    return (
        <>
            <Form.Item
                name={fieldPaths.userModifiedContent}
                hidden
            >
                <Input />
            </Form.Item>
            <Form.Item
                name={fieldPaths.userHasModified}
                hidden
            >
                <Input />
            </Form.Item>
            <Form.Item
                label="鉴权方法"
                name={fieldPaths.authDescription}
                rules={formConfig.rules}
                validateTrigger={['onBlur', 'onChange']}
            >
                <Input.TextArea
                    placeholder={formConfig.placeholder}
                    autoSize={{minRows: 3}}
                    onChange={handleInputChange}
                />
            </Form.Item>
        </>
    );
};

export default AuthDescriptionField;
