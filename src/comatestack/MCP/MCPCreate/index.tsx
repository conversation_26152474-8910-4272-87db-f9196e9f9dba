/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Form, Space} from 'antd';
import {useCallback, useMemo, useState} from 'react';
import {Button, message} from '@panda-design/components';
import {useSearchParams} from '@panda-design/router';
import {useNavigate} from 'react-router-dom';
import {useBoolean} from 'huse';
import {Gap} from '@/design/iplayground/Gap';
import {MCPServerType, MCPEditTab} from '@/types/mcp/mcp';
import {MCPEditLink, MCPSpaceLink} from '@/links/mcp';
import BaseContent from './BaseContent';
import ImportField from './OpenApiFields/ImportField';
import {useHandleCreateMCP} from './hooks';

const WrapperForm = styled(Form)`
    padding: 24px 16px !important;
    min-height: calc(100vh - 48px);
    max-width: 900px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
`;
const StyledTitle = styled.h2`
    color: #000;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 36px;
    margin: 0;
`;

const StyledButton = styled(Button)`
    color: #317ff5;
    border: 1px solid #317ff5;
`;

const ContentWrapper = styled.div`
    max-height: calc(100vh - 180px);
    padding-right: 16px;
    width: 100%;
    flex-grow: 1;
    overflow-y: auto;
`;

const modeTransfer: Record<MCPServerType, string> = {
    external: '已有MCP',
    openapi: 'OpenAPI转MCP',
    script: '脚本转MCP',
};

const MCPCreate = () => {
    const [form] = Form.useForm();
    const [loading, {on, off}] = useBoolean();
    const [stepNum, setStepNum] = useState(1);
    const {type: mode} = useSearchParams();
    const navigate = useNavigate();
    const createMCP = useHandleCreateMCP(form);

    const initialValues = useMemo(
        () => {
            return {
                serverSourceType: mode,
                serverConf: {
                    serverSourceType: mode,
                    ...(mode === 'openapi' ? {
                        serverExtension: {
                            serverAuthType: 'NONE',
                            authDescription: '',
                            _userModifiedContent: '',
                            _userHasModified: false,
                        },
                    } : {}),
                },
                ...(mode === 'openapi' ? {
                    serverProtocolType: 'Streamable_HTTP',
                } : (mode === 'script' ? {
                    serverProtocolType: 'STDIO',
                } : {
                    serverProtocolType: 'Streamable_HTTP',
                })),
                ...(mode !== 'external' ? {
                    import: {
                        type: 'openapi',
                    },
                } : {}),
                ...(mode === 'external' ? {
                    serverConfig: '{"mcpServers": {}}',
                } : {}),
            };
        },
        [mode]
    );

    const onFinish = useCallback(
        async (type: 'done' | 'toTool', mode: MCPServerType) => {
            on();
            createMCP(
                mcp => {
                    if (mcp.id) {
                        off();
                        message.success('创建成功');
                        if (type === 'done') {
                            const spaceLink = MCPSpaceLink.toUrl({workspaceId: mcp.workspaceId});
                            navigate(spaceLink);
                        } else {
                            const editLink = MCPEditLink.toUrl({
                                workspaceId: mcp.workspaceId,
                                mcpId: mcp.id,
                                activeTab: MCPEditTab.Tools,
                            });
                            navigate(editLink);
                        }
                    } else {
                        if ((mcp as any).response.data.code === 500) {
                            message.error((mcp as any).response.data.msg || '创建失败');
                        }
                        off();
                    }
                },
                off,
                mode
            );
        },
        [createMCP, navigate, off, on]
    );
    const nextStep = () => {
        setStepNum(stepNum + 1);
    };
    const prevStep = () => {
        setStepNum(stepNum - 1);
    };
    const handleCancel = useCallback(
        () => {
            form.resetFields();
            navigate(-1);
        },
        [form, navigate]
    );

    const onValuesChange = useCallback(
        (changedValues: any) => {
            const worksapceIdChanged = 'workspaceId' in changedValues;
            const labelsChanged = 'labels' in changedValues;
            // label是跟着workspaceid走的。但如果俩同时变了，那肯定是有业务上自己的调用，就不走这个逻辑了
            if (worksapceIdChanged && !labelsChanged) {
                form.setFieldValue('labels', []);
            }
        },
        [form]
    );

    const done = useCallback(
        () => {
            onFinish('done', mode as MCPServerType);
        },
        [onFinish, mode]
    );

    const toTool = useCallback(
        () => {
            onFinish('toTool', mode as MCPServerType);
        },
        [onFinish, mode]
    );

    return (
        <WrapperForm
            form={form}
            colon={false}
            labelCol={{flex: '120px'}}
            labelAlign="left"
            style={{width: '100%'}}
            initialValues={initialValues}
            onValuesChange={onValuesChange}
        >
            <StyledTitle>
                注册MCP
                <span style={{color: '#8f8f8f'}}>（{modeTransfer[mode as MCPServerType]}）</span>
            </StyledTitle>
            <ContentWrapper>
                <Gap />
                <BaseContent mode={mode} hidden={stepNum !== 1} />
                {mode === 'openapi' && <ImportField path={['serverConf']} hidden={stepNum !== 2} />}
                <Form.Item name="serverSourceType" hidden />
                <Form.Item name={['serverConf', 'serverSourceType']} hidden />
                {/* {mode === 'script' && <BashToolsField hidden={stepNum !== 2} />} */}
            </ContentWrapper>
            <Space>
                {(mode === 'openapi' ? stepNum === 2 : true) && (

                    <Button type="primary" onClick={done} loading={loading}>完成</Button>
                )}
                {(mode === 'openapi' ? stepNum === 2 : true) && (
                    <StyledButton onClick={toTool} loading={loading}>去配置工具</StyledButton>
                )}
                {
                    mode === 'openapi' && (
                        <>
                            {stepNum === 1 && <Button onClick={nextStep} type="primary">下一步</Button>}
                            {stepNum === 2 && <Button onClick={prevStep}>上一步</Button>}
                        </>
                    )}
                <Button onClick={handleCancel}>取消</Button>
            </Space>
        </WrapperForm>
    );
};
export default MCPCreate;
