/* eslint-disable max-lines */
// 实际上是提交规则里面关于人工评审的选择项的类型，这个名字基本不知道什么意思
import {RepoType, FrozenStatus} from './repo';
import {CodeScanExemptType} from './repoScan';
import {SecLevel} from './dataset';

export type RepoSettingReviewSettingType = 'BOT_PASS_NOT_AUTO_SUBMIT'
    | 'ANY_HUMAN_SCORE'
    | 'GOOD_CODER_SCORE'
    | 'NOT_AUTHOR_HUMAN_SCORE'
    | 'NOT_AUTHOR_GOOD_CODER_SCORE';

export interface RepoSettingPipelineCheckConf {
    pipelineConfId: number;
    pipelineName: string;
    pipelineModule: string;
    branchPattern: string;
    isValid: boolean;
    isBlockMerge: boolean;
}

export interface RepoGroup {
    companyId: number;
    gerritId: string;
    gmtCreate: string;
    gmtModified: string;
    id: number;
    memberCount: number;
    name: string;
    productRole: null;
    repoCount: number;
    repoRole: {
        resource: string;
        role: Role;
    };
    type: string;
}

export interface RepoMember {
    chineseName: string;
    departmentName: string;
    gmtCreate: string;
    gmtModified: string;
    id: number;
    repoId: number;
    repoMemberRole: Role;
    userEmail: string;
    userName: string;
}

export type ThousandLinesBlockTimeType = 'WEEK' | 'MONTH' | 'SEASON' | 'YEAR';

interface ThousandLinesBlock {
    isOpen: boolean;
    timeType: ThousandLinesBlockTimeType;
    commentsCount: number;
    enableTime: string;
}

interface ReadRatioBlock {
    isOpen: boolean;
    haveReadProportion: number;
}

export interface RepoSettings {
    autoSubmit: boolean;
    currentWorkflow: string;
    defaultBranch: string;
    description: string;
    engineeringIsAI: boolean;
    engineeringIsToB: boolean;
    engineeringType: string;
    forbidMergedCommit: boolean;
    forbidSubmitToProtectedBranchByMerge: boolean;
    groups: RepoGroup[];
    ignoreAgileScore: boolean;
    isSmallflow: null;
    maxTopicLength: null;
    members: RepoMember[];
    needDuplicateCheck: boolean;
    needDuplicateScore: boolean;
    needGoodCoderQualification: boolean;
    needHumScore: boolean;
    needIcafeCard: boolean;
    needIntelligentPrediction: boolean;
    needMaintainabilityCheck: boolean;
    needMisraCheck: boolean;
    needMisraScore: boolean;
    needSecurityCheck: boolean;
    needSecurityScore: boolean;
    needSecurityStyleCheck: boolean;
    needSecurityStyleScore: boolean;
    needStaticCheck: boolean;
    needStaticScore: boolean;
    needSubmitCheck: boolean;
    needUtCheck: boolean;
    needUtGenerate: boolean;
    needUtGenerateScore: boolean;
    personal: boolean;
    needFileUtf8Check: boolean;
    needFileUtf8Score: boolean;
    needSaCheck: boolean;
    needSaScore: boolean;
    needStableapiCheck: boolean;
    needStableapiScore: boolean;
    needLanguageCheck: boolean;
    needLanguageScore: boolean;
    needStableBuildCheck: boolean;
    needStableBuildScore: boolean;
    needCommentRateCheck: boolean;
    needCommentRateScore: boolean;
    stableBuildDestBranches: string[] | null;
    reviewSetting: RepoSettingReviewSettingType;
    role: string;
    secret: RepoType;
    showMisraCheck: boolean;
    singleCommitPerReview: boolean;
    spaceState: string;
    submitStrategy: string;
    supportWorkflowList: string[];
    useProductSubmitSetting: boolean;
    utCovRate: number;
    utOverTime: number;
    needOscScore: boolean;
    checkAllPipeline: boolean;
    changePipelineCheckConf: RepoSettingPipelineCheckConf[];
    needVendorGoodCoderExemption: boolean;
    frozenStatus: FrozenStatus;
    thousandLinesBlock: ThousandLinesBlock | null;
    haveReadBlock: ReadRatioBlock | null;
    mustBeFixedBlock: boolean;
    secLevel: SecLevel | null;
    cname: string;
    allowMergeRequestForbidChangeRequest: boolean;
}

export interface ICloudAccountState {
    accountId: string;
    parentPdbId: string;
}

export interface RepoVisibilityState {
    visibility: RepoType;
    reason: string;
}

export interface ICloudAccountProcess {
    applicant: string;
    formerLuoShuAccountId: string;
    gmtCreate: string;
    gmtModified: string;
    latestLuoShuAccountId: string;
    latestLuoShuAccountName: string;
    pdbProductId: number;
    processId: string;
    processStatus: string;
    refuseReason: string;
    repoApprover: string;
    repoName: string;
}

export interface ICloudAccountInfo {
    accountName: string;
    namePath: string;
    pdbId: number;
    pdbName: string;
    uuid: string;
}

export interface LimitedReviewerRule {
    gmtCreate: string;
    gmtModified: string;
    id: number;
    path: string;
    repoName: string;
    reviewers: string[];
    // 为了类型推断
    edit?: boolean;
}

// NOTE DELIVERER 和 GRANTED_CHIEF 都已废弃
export type Role = 'ADMIN' | 'OWNER' | 'MEMBER' | 'CHIEF' | 'GRANTED_CHIEF' | 'DELIVERER';

// 获取推荐评审人列表
export interface SuggestReviewer {
    id: number;
    gmtCreate: string;
    gmtModified: string;
    repoId: number;
    userName: string;
    repoMemberRole: Role;
    chineseName: string;
    departmentName: string;
    userEmail: string;
}

export interface DefaultReviewerRule {
    filter: string;
    reviewers: string[];
    // 前端手动添加
    id: number;
    edit?: boolean;
}

export interface AuditLog {
    detail: string;
    detailEmail: string;
    detailName: string;
    detailRole: string;
    id: number;
    operation: string;
    operator: string;
    operatorCname: string;
    operatorEmail: string;
    repoName: string;
    time: string;
}


export type ExemptKind = 'style' | 'defect';

export type LanguageExemptKind = 'language';

export type ApiGovExemptKind = 'apiGov';

export type LanguageOrApiGovExemptType = 'TEMPORARY' | 'PERMANENT';

export interface ExemptOption {
    value: ExemptKind | LanguageExemptKind | ApiGovExemptKind;
    label: string;
    id: CodeScanExemptType;
}
