/**
 * @file 二级目录类型
 */
import {RepoInfo} from './repo';

export interface ProductSyncDetail {
    operateTime: null;
    operator: null;
    productId: number;
    repoIdsToSync: unknown[];
    synchronizing: boolean;
}

export interface ProductSetting {
    admins: string[];
    applySubmitSetting: boolean;
    createRepoRole: string;
    deliverers: unknown[];
    members: unknown[];
    owners: unknown[];
    productName: string;
    repos: RepoInfo[];
    syncDetail: ProductSyncDetail;
}

export interface Product {
    applySubmitSetting: boolean;
    companyId: number;
    createRepoRole: string;
    gmtCreate: string;
    gmtModified: string;
    id: number;
    name: string;
    personalCode: boolean;
}

export interface ProductSubmitSetting {
    autoSubmit: boolean;
    checkCodeStyle: boolean;
    forbidMergedCommit: boolean;
    forbidSubmitToProtectedBranchByMerge: boolean;
    ignoreAgileScore: boolean;
    needBuildCodeScan: null;
    needBuildCodeScore: null;
    needDuplicateCheck: boolean;
    needDuplicateScore: boolean;
    needGoodCoderQualification: boolean;
    needIcafeCard: boolean;
    needIntelligentPrediction: boolean;
    needMaintainabilityCheck: boolean;
    needMisraCheck: boolean;
    needMisraScore: boolean;
    needSecurityCheck: boolean;
    needSecurityScore: boolean;
    needSecurityStyleCheck: boolean;
    needSecurityStyleScore: boolean;
    needStaticCheck: boolean;
    needStaticCodeScan: null;
    needStaticCodeScore: null;
    needStaticScore: boolean;
    needSubmitCheck: boolean;
    needUtCheck: boolean;
    needUtGenerate: boolean;
    needUtGenerateScore: boolean;
    needLanguageCheck: boolean;
    needLanguageScore: boolean;
    needStableBuildCheck: boolean;
    needStableBuildScore: boolean;
    stableBuildDestBranches: string[] | null;
    reviewSetting: string;
    singleCommitPerReview: boolean;
    sourceType: string;
    submitStrategy: string;
    utCovRate: number;
    utOverTime: number;
    needVendorGoodCoderExemption: boolean;
    allowMergeRequestForbidChangeRequest: boolean;
}

export interface ExemptionRepo {
    gmtCreate: string;
    gmtModified: string;
    id: number;
    operator: string;
    productId: number;
    reason: string;
    repoId: number;
    repoName: string;
}
