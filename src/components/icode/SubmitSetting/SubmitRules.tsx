import {partial} from 'lodash';
import {Button} from '@panda-design/components';
import {Space} from 'antd';
import {PlayCircleOutlined} from '@ant-design/icons';
import {useFieldDefaultProps, useFieldHandler, useFieldValue, useFormContext} from '@panda-design/path-form';
import {CIHelpLink} from '@/links/icode/external';
import {RepoSettings} from '@/types/icode/repoSetting';
import {GoodCoderHelpMessage, MergeHelpMessage, MergeRequestForbidHelpMessage, RebaseHelpMessage} from './HelpMessages';
import {CheckboxBlock} from './CheckboxBlock';
import {List} from './List';

export const SubmitRules = () => {
    const {initialValues} = useFormContext<RepoSettings>();
    const {disabled} = useFieldDefaultProps();
    const needSubmitCheck = useFieldValue('needSubmitCheck');
    const handleNeedSubmitCheck = useFieldHandler('needSubmitCheck');
    const forbidMergedCommit = useFieldValue('forbidMergedCommit');
    const handleForbidMergedCommit = useFieldHandler('forbidMergedCommit');
    const forbidSubmitToProtectedBranchByMerge = useFieldValue('forbidSubmitToProtectedBranchByMerge');
    const handleForbidSubmitToProtectedBranchByMerge = useFieldHandler('forbidSubmitToProtectedBranchByMerge');
    const needGoodCoderQualification = useFieldValue('needGoodCoderQualification');
    const handleNeedGoodCoderQualification = useFieldHandler('needGoodCoderQualification');
    const needIcafeCard = useFieldValue('needIcafeCard');
    const handleNeedIcafeCard = useFieldHandler('needIcafeCard');
    const singleCommitPerReview = useFieldValue('singleCommitPerReview');
    const handleSingleCommitPerReview = useFieldHandler('singleCommitPerReview');
    const needVendorGoodCoderExemption = useFieldValue('needVendorGoodCoderExemption');
    const handleNeedVendorGoodCoderExemption = useFieldHandler('needVendorGoodCoderExemption');
    const allowMergeRequestForbidChangeRequest = useFieldValue('allowMergeRequestForbidChangeRequest');
    const handleAllowMergeRequestForbidChangeRequest = useFieldHandler('allowMergeRequestForbidChangeRequest');

    return (
        <List title="提交">
            <CheckboxBlock
                checked={needSubmitCheck}
                onChange={handleNeedSubmitCheck}
                disabled={disabled}
                text="仅允许 git push origin HEAD:refs/for/xxx"
            >
                <CIHelpLink>
                    <Button size="small" type="primary">
                        <PlayCircleOutlined />观看iCode提交模型
                    </Button>
                </CIHelpLink>
            </CheckboxBlock>
            <CheckboxBlock
                checked={forbidSubmitToProtectedBranchByMerge}
                onChange={handleForbidSubmitToProtectedBranchByMerge}
                disabled={disabled}
                text="禁止在网页上直接merge修改master分支和默认分支"
            />
            <CheckboxBlock
                checked={needIcafeCard}
                onChange={handleNeedIcafeCard}
                // 当 needIcafeCard 已经被设置为 true，则不允许修改
                // 需要加上 diffState.needIcafeCard === undefined 来判断是否是编辑中导致值为 true 的状态
                disabled={disabled || initialValues.needIcafeCard}
                text="commit message 必须包含 iCafe 卡片 ID，格式为 name-1234"
                tooltip="根据公司技术治理规范，当前提交绑卡禁止取消，有问题请联系iCode服务号"
            />
            <CheckboxBlock
                checked={forbidMergedCommit}
                onChange={handleForbidMergedCommit}
                disabled={disabled}
                text="禁止 push 带有 merge 的提交，保持评审历史线性"
                tooltip={<MergeHelpMessage />}
            />
            <CheckboxBlock
                checked={singleCommitPerReview}
                onChange={handleSingleCommitPerReview}
                disabled={disabled}
                text="一次只能提交一个 commit"
                tooltip={<RebaseHelpMessage />}
            />
            <CheckboxBlock
                checked={allowMergeRequestForbidChangeRequest}
                onChange={handleAllowMergeRequestForbidChangeRequest}
                disabled={disabled}
                text="允许MergeRequest提交同时禁止ChangeRequest提交"
                tooltip={<MergeRequestForbidHelpMessage />}
            />
            <Space>
                <CheckboxBlock
                    checked={needGoodCoderQualification}
                    onChange={handleNeedGoodCoderQualification}
                    disabled={disabled}
                    text="仅允许Good Coder提交代码"
                    tooltip={<GoodCoderHelpMessage />}
                />
                {needGoodCoderQualification && (
                    <CheckboxBlock
                        text="允许外包豁免"
                        onChange={partial(handleNeedVendorGoodCoderExemption, !needVendorGoodCoderExemption)}
                        checked={needVendorGoodCoderExemption}
                    />
                )}
            </Space>
        </List>
    );
};
