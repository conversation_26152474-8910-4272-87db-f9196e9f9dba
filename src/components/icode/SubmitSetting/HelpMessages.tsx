/**
 * @file 提交规则帮助信息
 */
import {
    GoodCoderDocPageLink,
    OpenSourceLink,
    BugbyeLink,
    SecurityHelpLink,
    SecurityStyleHelpLink,
    ExemptionsHelpLink,
    CovLink,
    SaLink,
    PipelineSettingLink,
    IPipeLink,
    LanguageCheckHelpLink,
    ComateCovUtGenerateDocLink,
    MergeRequestDocLink,
} from '@/links/icode/external';

export const MergeHelpMessage = () => {
    return (
        <div>
            <div>1.  Merge 过多易造成 git log 呈现网状历史，难以追溯</div>
            <div>2.  评审中的 merge 常常由 git pull 自动产生，使用 git pull --rebase 避免</div>
            <div>3.  如果需要经常在本地 git merge 分支，不宜开启这个限制</div>
        </div>
    );
};

export const RebaseHelpMessage = () => {
    return (
        <div>
            <div>1.  使用 git commit --amend 避免生成多余 commit</div>
            <div>2.  使用 git pull --rebase 更新，避免 merge 生成多余 commit</div>
        </div>
    );
};

export const GoodCoderHelpMessage = () => {
    return (
        <div>
            <div>覆盖语言：C++、Java、PHP、Python</div>
            <div>实际效果：非Good Coder通过git push向此代码库发评审失败.</div>
            <div>
                如何获得Good Coder，参考
                <GoodCoderDocPageLink>
                    Good Coder 认证制度
                </GoodCoderDocPageLink>
            </div>
        </div>
    );
};

export const SecurityHelpMessage = () => {
    return (
        <div>
            <div>强制开启</div>
            <div>
                检查服务：
                <SecurityHelpLink>
                    第三方高危开源软件检查
                </SecurityHelpLink>
            </div>
            <div>检查方式：增量扫描</div>
        </div>
    );
};

export const StableApiMessage = () => {
    return (
        <div>
            <div>覆盖语言：java</div>
            <div>检查方式: 增量扫描</div>
        </div>
    );
};

export const SecurityStyleHelpMessage = () => {
    return (
        <div>
            <div>强制开启</div>
            <div>覆盖语言：PHP, Java, JS, Go, 泛FE框架</div>
            <div>
                检查服务：
                <SecurityStyleHelpLink>
                    安全编码规范检查
                </SecurityStyleHelpLink>
            </div>
            <div>检查方式：增量扫描</div>
        </div>
    );
};

export const LanguageCheckHelpMessage = () => {
    return (
        <div>
            <div>强制开启</div>
            <div>
                检查服务：
                <LanguageCheckHelpLink>
                    研发技术栈收敛
                </LanguageCheckHelpLink>
            </div>
            <div>检查方式：增量扫描</div>
        </div>
    );
};

export const ExemptionsHelpMessage = () => {
    return (
        <div>
            <div>强制开启</div>
            <div>
                规范检查：
                <ExemptionsHelpLink>
                    豁免知识库链接
                </ExemptionsHelpLink>
            </div>
        </div>
    );
};

export const StaticHelpMessage = () => {
    return (
        <div>
            <div>强制开启</div>
            <div>
                检查服务：
                <BugbyeLink>
                    bugbye.baidu.com
                </BugbyeLink>
            </div>
            <div>检查方式：增量扫描</div>
        </div>
    );
};

export const MisraCheckMessage = () => {
    return (
        <div>
            <div>
                检查服务：pinpoint，针对汽车工业软件安全性的C/C++语言编程规范，对安全性要求极高的嵌入式软件应符合MISRA C/C++规范
            </div>
            <div>检查方式：增量扫描</div>
        </div>
    );
};

// 代码可维护性指数(MI)检查 选项帮助信息
export const MaintainabilityHelpMessage = () => {
    return (
        <div>
            <div>
                检查服务：
                <BugbyeLink>
                    bugbye.baidu.com
                </BugbyeLink>
            </div>
            <div>检查方式：增量扫描</div>
        </div>
    );
};

export const UtHelpMessage = () => {
    return (
        <div>
            <div>覆盖语言：C++、Java、PHP、Python、Go</div>
            <div>检查服务：<CovLink>Cov</CovLink></div>
            <div>检查方式：增量扫描</div>
        </div>
    );
};

export const SaHelpMessage = () => {
    return (
        <div>
            <div>覆盖语言：C++、Go、JS、Python、Java</div>
            <div>检查服务：<SaLink>W_BOX</SaLink></div>
            <div>检查方式：增量扫描</div>
        </div>
    );
};

export const GoodCoderScoreHelpMessage = () => {
    return (
        <div>
            <div>覆盖语言：C++、Java、PHP、Python、OC、JS、Shell、Go、Scala</div>
            <div>开启前提：必须先开启“必须有评审人+2”的开关</div>
            <div>实际效果：非Good Coder +2不算评审通过，必须有Good Coder给出+2且无任何-2分才算评审通过.</div>
            <div>
                如何获得Good Coder，参考
                <GoodCoderDocPageLink>
                    Good Coder 认证制度
                </GoodCoderDocPageLink>
            </div>
        </div>
    );
};
export const OpenSourceHelpMessage = () => {
    return (
        <div>
            <div>强制开启</div>
            <div>检查方式：增量扫描</div>
            <div>
                检查服务：
                <OpenSourceLink>
                    开源协议合规检查
                </OpenSourceLink>
            </div>
        </div>
    );
};

export const PipelineHelpMessage = () => {
    return (
        <div>
            <div>检查服务：<IPipeLink>iPipe</IPipeLink></div>
            <div>实际效果：选择影响合入的流水线</div>
            <div>
                功能介绍：
                <PipelineSettingLink>
                    查看文档
                </PipelineSettingLink>
            </div>
        </div>
    );
};

export const ThousandLinesBlockHelpMessage = () => {
    return (
        <div>
            <p>用户需要在一定时间范围内，千行评论数达到一定数量，才可以进行+2打分的操作。</p>
            <p>未到达数量不可以+2，但可以打其他分数。</p>
        </div>
    );
};

export const ReadRatioBlockHelpMessage = () => {
    return (
        <div>
            <p>用户需要阅读完一定百分比的文件，才可以进行+2打分的操作。</p>
            <p>未到达数量不可以+2，但可以打其他分数。</p>
        </div>
    );
};

export const MustBeFixedBlockHelpMessage = () => {
    return (
        <div>
            开启后，评审⼈标记某次评审或某段代码⽚段为 【需本次修复】，该评审⼈必须对当前评审 +2 后才可以合入。
        </div>
    );
};

export const StableBuildHelpMessage = () => {
    return (
        <div>
            <div>覆盖语言：Go</div>
            <div>特别注意：C/C++模块不要开启该选项</div>
            <div>开启后，往指定的目标分支提交CR会触发所有依赖当前模块的业务模块进行反向构建</div>
        </div>
    );
};

export const CommentRateHelpMessage = () => {
    return (
        <div>
            <div>覆盖语言：C、Cpp、JS、TS、Python、Java、Go</div>
            <div>开启后，会检测函数注释率，并使用comate为函数生成doc级注释。以及检查Java代码库的接口注释，并使用大模型为接口生成注释</div>
        </div>
    );
};

export const CommentUtGenerateHelpMessage = () => {
    return (
        <div>
            <div>覆盖语言：Java、Go</div>
            <div>提供服务：<ComateCovUtGenerateDocLink>Comate</ComateCovUtGenerateDocLink></div>
            <div>执行逻辑：评审合入后，自动为合入的代码生成可用的单测Case，并以原评审提交人名义发出新的评审。
                自动发出的评审无需人工打分，可设置为「手动合入」或「自动合入」；
                若设置自动合入，将忽略编码规范或流水线等机器检查结果，由系统直接合入。
            </div>
        </div>
    );
};

export const MergeRequestForbidHelpMessage = () => {
    return (
        <div>
            <div>勾选该配置后，禁止CR发评审操作，只能使用Merge Request进行变更</div>
            <div>
                使用手册，参考
                <MergeRequestDocLink>Merge Request</MergeRequestDocLink>
            </div>
        </div>
    );
};
