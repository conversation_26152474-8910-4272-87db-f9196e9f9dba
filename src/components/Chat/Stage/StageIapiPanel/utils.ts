/* eslint-disable max-lines */
import {DirectoryResponseData, ImportResult, Team} from '@/api/icode/apidoc';

type IHttpFolderOption = DirectoryResponseData & {
  value: number;
  children?: IHttpFolderOption[];
};

// 遍历树结构
function traverseTree(tree: IHttpFolderOption[], callback: (node: IHttpFolderOption) => void) {
    for (const node of tree) {
        callback(node);
        if (node.children && node.children.length > 0) {
            traverseTree(node.children, callback);
        }
    }
}

export function transformTreeData(data: DirectoryResponseData[], root: DirectoryResponseData) {
    const treeData: IHttpFolderOption[] = [];
    for (const folder of data) {
    // 根节点
        if (folder.parentId === root.id || folder.parentId === 0) {
            treeData.push({...folder, value: folder.id, children: []});
        } else {
            traverseTree(treeData, node => {
                if (node.id === folder.parentId) {
                    if (!node.children) {
                        node.children = [];
                    }
                    node.children.push({...folder, value: folder.id, children: []});
                }
            });
        }
    }
    return treeData;
}

// 是否存在teamId和projectId
export function hasTeamIdAndProjectId(data: Team[], teamId: number, projectId: number) {
    const index = data.findIndex(item => item.id === teamId);
    if (index !== -1) {
        return data[index].children.findIndex(item => item.id === projectId) !== -1;
    }
    return false;
}

// 合并导入结果
export function mergeImportResult(result1: ImportResult, result2: ImportResult) {
    return {
        apiCollection: {
            item: {
                createCount:
            result1?.apiCollection?.item?.createCount + result2?.apiCollection?.item?.createCount,
                updateCount:
            result1?.apiCollection?.item?.updateCount + result2?.apiCollection?.item?.updateCount,
                errorCount:
            result1?.apiCollection?.item?.errorCount + result2?.apiCollection?.item?.errorCount,
                ignoreCount:
            result1?.apiCollection?.item?.ignoreCount + result2?.apiCollection?.item?.ignoreCount,
                errorIds: [
                    ...result1?.apiCollection?.item?.errorIds,
                    ...result2?.apiCollection?.item?.errorIds,
                ],
            },
            folder: {
                createCount:
            result1?.apiCollection?.folder?.createCount + result2?.apiCollection?.folder?.createCount,
                updateCount:
            result1?.apiCollection?.folder?.updateCount + result2?.apiCollection?.folder?.updateCount,
                errorCount:
            result1?.apiCollection?.folder?.errorCount + result2?.apiCollection?.folder?.errorCount,
                ignoreCount:
            result1?.apiCollection?.folder?.ignoreCount + result2?.apiCollection?.folder?.ignoreCount,
                errorIds: [
                    ...result1?.apiCollection?.folder?.errorIds,
                    ...result2?.apiCollection?.folder?.errorIds,
                ],
            },
        },
        webSocketCollection: {
            item: {
                createCount:
            result1?.webSocketCollection?.item?.createCount
            + result2?.webSocketCollection?.item?.createCount,
                updateCount:
            result1?.webSocketCollection?.item?.updateCount
            + result2?.webSocketCollection?.item?.updateCount,
                errorCount:
            result1?.webSocketCollection?.item?.errorCount
            + result2?.webSocketCollection?.item?.errorCount,
                ignoreCount:
            result1?.webSocketCollection?.item?.ignoreCount
            + result2?.webSocketCollection?.item?.ignoreCount,
                errorIds: [
                    ...result1?.webSocketCollection?.item?.errorIds,
                    ...result2?.webSocketCollection?.item?.errorIds,
                ],
            },
            folder: {
                createCount:
            result1?.webSocketCollection?.folder?.createCount
            + result2?.webSocketCollection?.folder?.createCount,
                updateCount:
            result1?.webSocketCollection?.folder?.updateCount
            + result2?.webSocketCollection?.folder?.updateCount,
                errorCount:
            result1?.webSocketCollection?.folder?.errorCount
            + result2?.webSocketCollection?.folder?.errorCount,
                ignoreCount:
            result1?.webSocketCollection?.folder?.ignoreCount
            + result2?.webSocketCollection?.folder?.ignoreCount,
                errorIds: [
                    ...result1?.webSocketCollection?.folder?.errorIds,
                    ...result2?.webSocketCollection?.folder?.errorIds,
                ],
            },
        },
        socketCollection: {
            service: {
                createCount:
            result1?.socketCollection?.service?.createCount
            + result2?.socketCollection?.service?.createCount,
                updateCount:
            result1?.socketCollection?.service?.updateCount
            + result2?.socketCollection?.service?.updateCount,
                errorCount:
            result1?.socketCollection?.service?.errorCount
            + result2?.socketCollection?.service?.errorCount,
                ignoreCount:
            result1?.socketCollection?.service?.ignoreCount
            + result2?.socketCollection?.service?.ignoreCount,
                errorIds: [
                    ...result1?.socketCollection?.service?.errorIds,
                    ...result2?.socketCollection?.service?.errorIds,
                ],
            },
            api: {
                createCount:
            result1?.socketCollection?.api?.createCount + result2?.socketCollection?.api?.createCount,
                updateCount:
            result1?.socketCollection?.api?.updateCount + result2?.socketCollection?.api?.updateCount,
                errorCount:
            result1?.socketCollection?.api?.errorCount + result2?.socketCollection?.api?.errorCount,
                ignoreCount:
            result1?.socketCollection?.api?.ignoreCount + result2?.socketCollection?.api?.ignoreCount,
                errorIds: [
                    ...result1?.socketCollection?.api?.errorIds,
                    ...result2?.socketCollection?.api?.errorIds,
                ],
            },
            folder: {
                createCount:
            result1?.socketCollection?.folder?.createCount
            + result2?.socketCollection?.folder?.createCount,
                updateCount:
            result1?.socketCollection?.folder?.updateCount
            + result2?.socketCollection?.folder?.updateCount,
                errorCount:
            result1?.socketCollection?.folder?.errorCount
            + result2?.socketCollection?.folder?.errorCount,
                ignoreCount:
            result1?.socketCollection?.folder?.ignoreCount
            + result2?.socketCollection?.folder?.ignoreCount,
                errorIds: [
                    ...result1?.socketCollection?.folder?.errorIds,
                    ...result2?.socketCollection?.folder?.errorIds,
                ],
            },
        },
        docCollection: {
            item: {
                createCount:
            result1?.docCollection?.item?.createCount + result2?.docCollection?.item?.createCount,
                updateCount:
            result1?.docCollection?.item?.updateCount + result2?.docCollection?.item?.updateCount,
                errorCount:
            result1?.docCollection?.item?.errorCount + result2?.docCollection?.item?.errorCount,
                ignoreCount:
            result1?.docCollection?.item?.ignoreCount + result2?.docCollection?.item?.ignoreCount,
                errorIds: [
                    ...result1?.docCollection?.item?.errorIds,
                    ...result2?.docCollection?.item?.errorIds,
                ],
            },
            folder: {
                createCount:
            result1?.docCollection?.folder?.createCount + result2?.docCollection?.folder?.createCount,
                updateCount:
            result1?.docCollection?.folder?.updateCount + result2?.docCollection?.folder?.updateCount,
                errorCount:
            result1?.docCollection?.folder?.errorCount + result2?.docCollection?.folder?.errorCount,
                ignoreCount:
            result1?.docCollection?.folder?.ignoreCount + result2?.docCollection?.folder?.ignoreCount,
                errorIds: [
                    ...result1?.docCollection?.folder?.errorIds,
                    ...result2?.docCollection?.folder?.errorIds,
                ],
            },
        },
        schemaCollection: {
            item: {
                createCount:
            result1?.schemaCollection?.item?.createCount
            + result2?.schemaCollection?.item?.createCount,
                updateCount:
            result1?.schemaCollection?.item?.updateCount
            + result2?.schemaCollection?.item?.updateCount,
                errorCount:
            result1?.schemaCollection?.item?.errorCount + result2?.schemaCollection?.item?.errorCount,
                ignoreCount:
            result1?.schemaCollection?.item?.ignoreCount
            + result2?.schemaCollection?.item?.ignoreCount,
                errorIds: [
                    ...result1?.schemaCollection?.item?.errorIds,
                    ...result2?.schemaCollection?.item?.errorIds,
                ],
            },
            folder: {
                createCount:
            result1?.schemaCollection?.folder?.createCount
            + result2?.schemaCollection?.folder?.createCount,
                updateCount:
            result1?.schemaCollection?.folder?.updateCount
            + result2?.schemaCollection?.folder?.updateCount,
                errorCount:
            result1?.schemaCollection?.folder?.errorCount
            + result2?.schemaCollection?.folder?.errorCount,
                ignoreCount:
            result1?.schemaCollection?.folder?.ignoreCount
            + result2?.schemaCollection?.folder?.ignoreCount,
                errorIds: [
                    ...result1?.schemaCollection?.folder?.errorIds,
                    ...result2?.schemaCollection?.folder?.errorIds,
                ],
            },
        },
        testCaseCollection: {
            item: {
                createCount:
            result1?.testCaseCollection?.item?.createCount
            + result2?.testCaseCollection?.item?.createCount,
                updateCount:
            result1?.testCaseCollection?.item?.updateCount
            + result2?.testCaseCollection?.item?.updateCount,
                errorCount:
            result1?.testCaseCollection?.item?.errorCount
            + result2?.testCaseCollection?.item?.errorCount,
                ignoreCount:
            result1?.testCaseCollection?.item?.ignoreCount
            + result2?.testCaseCollection?.item?.ignoreCount,
                errorIds: [
                    ...result1?.testCaseCollection?.item?.errorIds,
                    ...result2?.testCaseCollection?.item?.errorIds,
                ],
            },
            folder: {
                createCount:
            result1?.testCaseCollection?.folder?.createCount
            + result2?.testCaseCollection?.folder?.createCount,
                updateCount:
            result1?.testCaseCollection?.folder?.updateCount
            + result2?.testCaseCollection?.folder?.updateCount,
                errorCount:
            result1?.testCaseCollection?.folder?.errorCount
            + result2?.testCaseCollection?.folder?.errorCount,
                ignoreCount:
            result1?.testCaseCollection?.folder?.ignoreCount
            + result2?.testCaseCollection?.folder?.ignoreCount,
                errorIds: [
                    ...result1?.testCaseCollection?.folder?.errorIds,
                    ...result2?.testCaseCollection?.folder?.errorIds,
                ],
            },
        },
        testSuiteCollection: {
            item: {
                createCount:
            result1?.testSuiteCollection?.item?.createCount
            + result2?.testSuiteCollection?.item?.createCount,
                updateCount:
            result1?.testSuiteCollection?.item?.updateCount
            + result2?.testSuiteCollection?.item?.updateCount,
                errorCount:
            result1?.testSuiteCollection?.item?.errorCount
            + result2?.testSuiteCollection?.item?.errorCount,
                ignoreCount:
            result1?.testSuiteCollection?.item?.ignoreCount
            + result2?.testSuiteCollection?.item?.ignoreCount,
                errorIds: [
                    ...result1?.testSuiteCollection?.item?.errorIds,
                    ...result2?.testSuiteCollection?.item?.errorIds,
                ],
            },
            folder: {
                createCount:
            result1?.testSuiteCollection?.folder?.createCount
            + result2?.testSuiteCollection?.folder?.createCount,
                updateCount:
            result1?.testSuiteCollection?.folder?.updateCount
            + result2?.testSuiteCollection?.folder?.updateCount,
                errorCount:
            result1?.testSuiteCollection?.folder?.errorCount
            + result2?.testSuiteCollection?.folder?.errorCount,
                ignoreCount:
            result1?.testSuiteCollection?.folder?.ignoreCount
            + result2?.testSuiteCollection?.folder?.ignoreCount,
                errorIds: [
                    ...result1?.testSuiteCollection?.folder?.errorIds,
                    ...result2?.testSuiteCollection?.folder?.errorIds,
                ],
            },
        },
        responseCollection: {
            item: {
                createCount:
            result1?.responseCollection?.item?.createCount
            + result2?.responseCollection?.item?.createCount,
                updateCount:
            result1?.responseCollection?.item?.updateCount
            + result2?.responseCollection?.item?.updateCount,
                errorCount:
            result1?.responseCollection?.item?.errorCount
            + result2?.responseCollection?.item?.errorCount,
                ignoreCount:
            result1?.responseCollection?.item?.ignoreCount
            + result2?.responseCollection?.item?.ignoreCount,
                errorIds: [
                    ...result1?.responseCollection?.item?.errorIds,
                    ...result2?.responseCollection?.item?.errorIds,
                ],
            },
            folder: {
                createCount:
            result1?.responseCollection?.folder?.createCount
            + result2?.responseCollection?.folder?.createCount,
                updateCount:
            result1?.responseCollection?.folder?.updateCount
            + result2?.responseCollection?.folder?.updateCount,
                errorCount:
            result1?.responseCollection?.folder?.errorCount
            + result2?.responseCollection?.folder?.errorCount,
                ignoreCount:
            result1?.responseCollection?.folder?.ignoreCount
            + result2?.responseCollection?.folder?.ignoreCount,
                errorIds: [
                    ...result1?.responseCollection?.folder?.errorIds,
                    ...result2?.responseCollection?.folder?.errorIds,
                ],
            },
        },
        environment: {
            createCount: result1?.environment?.createCount + result2?.environment?.createCount,
            updateCount: result1?.environment?.updateCount + result2?.environment?.updateCount,
            errorCount: result1?.environment?.errorCount + result2?.environment?.errorCount,
            ignoreCount: result1?.environment?.ignoreCount + result2?.environment?.ignoreCount,
            errorIds: [...result1?.environment?.errorIds, ...result2?.environment?.errorIds],
        },
    };
}
