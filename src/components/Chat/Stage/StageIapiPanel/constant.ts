/* eslint-disable max-lines */
const dataImportResult = {
    apiCollection: {
        item: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
        folder: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
    },
    webSocketCollection: {
        item: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
        folder: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
    },
    socketCollection: {
        service: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
        api: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
        folder: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
    },
    docCollection: {
        item: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
        folder: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
    },
    schemaCollection: {
        item: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
        folder: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
    },
    testCaseCollection: {
        item: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
        folder: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
    },
    testSuiteCollection: {
        item: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
        folder: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
    },
    responseCollection: {
        item: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
        folder: {
            createCount: 0,
            updateCount: 0,
            errorCount: 0,
            ignoreCount: 0,
            errorIds: [] as number[],
        },
    },
    environment: {
        createCount: 0,
        updateCount: 0,
        errorCount: 0,
        ignoreCount: 0,
        errorIds: [] as number[],
    },
};

export default dataImportResult;
