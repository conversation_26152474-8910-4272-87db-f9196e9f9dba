/* eslint-disable max-depth */
/* eslint-disable complexity */
/* eslint-disable max-statements */
/* eslint-disable max-lines */
import {Collapse, Spin, Tooltip} from 'antd';
import {Button} from '@panda-design/components';
import {useCallback, useEffect, useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {isEmpty} from 'lodash';
import {UpOutlined} from '@ant-design/icons';
import {
    ApiDocByTaskId,
    Team, getApiDocByTaskId,
    getImportableProjects,
    getApiDocByWorkId,
    importApiToProject,
    confirmByTaskId,
    confirmByWorkId,
    getMetaData,
    getProjectDirectories,
    DirectoryResponseData,
    getUserInfo,
    createNewTeamAndProject,
    ImportResult,
} from '@/api/icode/apidoc';
import {getSysToken} from '@/utils/icode/api/createBApiInterface';
import {useCurrentUser} from '@/regions/user/currentUser';
import ApiDocViewForStaff from '@/icode/ReviewDetails/ApiDocViewForStaff';
import {StageIapi} from '@/types/staff/stage';
import {SelectedApiInfo, SelectionState} from '@/icode/ReviewDetails/ApiDocViewForStaff/types';
import {SaveOption} from './SaveOption';
import {transformTreeData, hasTeamIdAndProjectId, mergeImportResult} from './utils';
import dataImportResult from './constant';

interface Props {
    stage: StageIapi;
}

const Container = styled.div`
    position: relative !important;
    padding: 10px !important;
    height: calc(100vh - 52px) !important;
    display: flex !important;
    flex-direction: column !important;
    background: #fff !important;
`;

const ContentArea = styled.div`
    flex: 1 1 0%;
    overflow-y: auto;

    ::-webkit-scrollbar {
        width: 4px !important;
    }

    ::-webkit-scrollbar-thumb {
        background-color: #E8E8E8 !important;
        border-radius: 4px !important;
    }
`;

const FooterBar = styled.div`
    margin-left: -10px;
    margin-right: -10px;
    padding: 16px 10px 6px 10px;
    border-top: 1px solid #E8E8E8;
    display: flex;
    justify-content: flex-start;
    z-index: 1000;
    background: #fff;
`;

const StyledButton = styled(Button)`
    height: 24px !important;
    padding: 0 12px !important;
    margin: 0 0 0 14px !important;
`;

const CustomizedCollapse = styled(Collapse)`
    .ant-5-collapse-header {
        display: flex !important;
        align-items: center !important;
        height: 32px !important;
        font-weight: 500 !important;
        padding: 10px 16px !important;
        border-radius: 6px !important;
        margin-bottom: 12px !important;
        background:
            linear-gradient(90deg, rgba(144, 166, 255, 0.12) 0%, rgba(199, 175, 255, 0.05) 100%),
            #ffffff !important;
        position: relative !important;
    }

    .ant-5-collapse-header-sticky {
        position: sticky !important;
        top: 0 !important;
        z-index: 1000 !important;
        margin-bottom: 6px !important;
        background:
            linear-gradient(90deg, rgba(144, 166, 255, 0.12) 0%, rgba(199, 175, 255, 0.05) 100%),
            #ffffff !important;

    }

    .ant-5-collapse-content-box {
        padding-bottom: 0 !important;
    }
`;

export const StageIapiPanel = ({stage}: Props) => {
    const {taskId, workId} = stage?.value;
    const username = useCurrentUser().username;
    const [loading, setLoading] = useState(true);
    const [projectOption, setProjectOption] = useState<Team[]>([]);
    const [teamIdAndProjectId, setTeamIdAndProjectId] = useState<number[]>();
    const [initialFolderOption, setInitialFolderOption] = useState<DirectoryResponseData[]>([]);
    const [folderOption, setFolderOption] = useState<DirectoryResponseData[]>([]);
    const [folderId, setFolderId] = useState<number>();
    const [interfaceSign, setInterfaceSign] = useState(null);
    const [apiOverwriteMode, setApiOverwriteMode] = useState<string>('');
    const [apiData, setApiData] = useState<ApiDocByTaskId[]>([]);
    const [selectedApis, setSelectedApis] = useState<SelectedApiInfo[]>([]);
    const [selectionState, setSelectionState] = useState<SelectionState>({
        selectedApiIds: [],
        currentApiId: null,
        expandedCatalogs: [],
    });
    const [activeKey, setActiveKey] = useState(['1', '2']);

    const initData = useCallback(
        async () => {
            try {
                const userInfo = await getUserInfo({
                    params: {
                        sysToken: getSysToken(),
                    },
                    headers: {
                        'x-baidu-username': username,
                    },
                });
                if (!isEmpty(userInfo)) {
                    // 没有用过iAPI的新用户
                    if ((new Date().getTime() - new Date(userInfo[0].createdAt).getTime()) / 1000 <= 60) {
                        await createNewTeamAndProject({
                            params: {
                                sysToken: getSysToken(),
                            },
                            headers: {
                                'x-baidu-username': username,
                                'Content-type': 'application/json',
                            },
                        });
                    }
                    const teamAndProjects = await getImportableProjects({
                        params: {
                            sysToken: getSysToken(),
                        },
                        headers: {
                            'x-baidu-username': username,
                        },
                    });
                    setProjectOption(teamAndProjects);

                    const metaData = await getMetaData({
                        params: {
                            source: workId ? 'code_based_api_doc_generator_tool' : 'doc_based_api_doc_generator_tool',
                            workId: workId ? workId : taskId,
                        },
                    });
                    const iAPISelectTeamId = Number(localStorage.getItem('iAPISelectTeamId'));
                    const iAPISelectProjectId = Number(localStorage.getItem('iAPISelectProjectId'));
                    let projectId = 0;
                    if (!metaData) {
                        const isHasTeamIdAndProjectId = hasTeamIdAndProjectId(
                            teamAndProjects, iAPISelectTeamId, iAPISelectProjectId
                        );
                        if (isHasTeamIdAndProjectId) {
                            setTeamIdAndProjectId([iAPISelectTeamId, iAPISelectProjectId]);
                            projectId = iAPISelectProjectId;
                        } else {
                            setTeamIdAndProjectId([teamAndProjects[0].id, teamAndProjects[0].children[0].id]);
                            projectId = teamAndProjects[0].children[0].id;
                        }
                    } else {
                        const metaTeamId = Number(metaData.teamId);
                        const metaProjectId = Number(metaData.projectId);
                        const isHasTeamIdAndProjectId = hasTeamIdAndProjectId(
                            teamAndProjects, metaTeamId, metaProjectId
                        );
                        if (isHasTeamIdAndProjectId) {
                            setTeamIdAndProjectId([metaTeamId, metaProjectId]);
                            projectId = metaProjectId;
                        } else {
                            const isHasTeamIdAndProjectId = hasTeamIdAndProjectId(
                                teamAndProjects, iAPISelectTeamId, iAPISelectProjectId
                            );
                            if (isHasTeamIdAndProjectId) {
                                setTeamIdAndProjectId([iAPISelectTeamId, iAPISelectProjectId]);
                                projectId = iAPISelectProjectId;
                            } else {
                                setTeamIdAndProjectId([teamAndProjects[0].id, teamAndProjects[0].children[0].id]);
                                projectId = teamAndProjects[0].children[0].id;
                            }
                        }
                    }

                    const directory = await getProjectDirectories({
                        headers: {
                            'x-baidu-username': username,
                            'x-project-id': String(projectId),
                        },
                        params: {
                            sysToken: getSysToken(),
                        },
                    });
                    setInitialFolderOption(directory);
                    setFolderOption(transformTreeData(directory, directory[0]));
                    setFolderId(directory[0].id);
                    if (!interfaceSign) {
                        setInterfaceSign(directory[0].identityPattern.httpApi);
                    }

                    if (!apiOverwriteMode) {
                        setApiOverwriteMode('methodAndPath');
                    }

                    const apiData = workId ? await getApiDocByWorkId({
                        params: {
                            workId,
                            source: 'code_based_api_doc_generator_tool',
                        },
                    }) : await getApiDocByTaskId({
                        params: {
                            taskId,
                        },
                    });
                    setApiData(apiData);
                    setLoading(false);
                }
            } catch (err) {
                setLoading(false);
            }
            const collapseHeaders: NodeListOf<HTMLDivElement> = document.querySelectorAll('.ant-5-collapse-header');
            collapseHeaders[1].classList.add('ant-5-collapse-header-sticky');
        },
        [apiOverwriteMode, interfaceSign, taskId, username, workId]
    );

    const handleSave = useCallback(
        async () => {
            setLoading(true);
            const apiList = [];
            let importResult = dataImportResult as ImportResult;
            for (const [index, api] of selectedApis.entries()) {
                const result = await importApiToProject({
                    params: {
                        sysToken: getSysToken(),
                    },
                    headers: {
                        'x-baidu-username': username,
                        'x-client-version': '3.0.0',
                        'Content-type': 'application/json',
                    },
                    payload: {
                        data: api.swaggerContent,
                        importFormat: 'openapi',
                        source: 'APIAgent',
                        apiOverwriteMode,
                        schemaOverwriteMode: 'both',
                        apiFolderId: folderId,
                        schemaFolderId: 0,
                        importApiCase: false,
                        testCaseFolderId: 0,
                        responseFolderId: 0,
                        isNotified: index === selectedApis.length - 1,
                        dataImportResult: index === selectedApis.length - 1
                            ? selectedApis.length === 1
                                ? undefined
                                : importResult
                            : undefined,
                    },
                    projectId: String(teamIdAndProjectId[1]),
                });
                if (index !== selectedApis.length - 1) {
                    importResult = mergeImportResult(importResult, result);
                }
                if (!isEmpty(result?.httpApiInfo)) {
                    apiList.push({
                        taskId: api.taskId,
                        apiParseId: api.apiParseId,
                        accepted: api.accepted,
                        iapiName: result?.httpApiInfo.name ?? '',
                        iapiMethod: result?.httpApiInfo.method?.toUpperCase() ?? '',
                        iapiId: String(result?.httpApiInfo.id ?? 0),
                        iapiFolderId: String(folderId),
                        iapiProjectId: String(teamIdAndProjectId[1] ?? 0),
                    });
                }
            }

            if (taskId) {
                await confirmByTaskId({
                    headers: {
                        'Content-type': 'application/json',
                    },
                    payload: apiList,
                    params: {
                        taskId,
                        savedByUser: username,
                    },
                });
            }

            if (workId) {
                await confirmByWorkId({
                    headers: {
                        'Content-type': 'application/json',
                    },
                    payload: apiList,
                    params: {
                        workId,
                        savedByUser: username,
                    },
                });
            }
            localStorage.setItem('iAPISelectTeamId', String(teamIdAndProjectId[0]));
            localStorage.setItem('iAPISelectProjectId', String(teamIdAndProjectId[1]));
            initData();
            setSelectionState(prev => ({
                ...prev,
                selectedApiIds: [],
            }));
            setLoading(false);
        },
        [apiOverwriteMode, folderId, initData, selectedApis, taskId, teamIdAndProjectId, username, workId]
    );

    useEffect(
        () => {
            // 只首次执行initData
            initData();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    const items = useMemo(
        () => {
            return [
                {
                    key: '1',
                    label: '保存选项',
                    children: <SaveOption
                        username={username}
                        projectOption={projectOption}
                        teamIdAndProjectId={teamIdAndProjectId}
                        setTeamIdAndProjectId={setTeamIdAndProjectId}
                        initialFolderOption={initialFolderOption}
                        setInitialFolderOption={setInitialFolderOption}
                        folderOption={folderOption}
                        setFolderOption={setFolderOption}
                        folderId={folderId}
                        setFolderId={setFolderId}
                        interfaceSign={interfaceSign}
                        setInterfaceSign={setInterfaceSign}
                        apiOverwriteMode={apiOverwriteMode}
                        setApiOverwriteMode={setApiOverwriteMode}
                    />,
                },
                {
                    key: '2',
                    label: '接口文档',
                    children: <ApiDocViewForStaff
                        apiData={apiData}
                        selectionState={selectionState}
                        setSelectionState={setSelectionState}
                        onSelectionChange={selectedApis => setSelectedApis(selectedApis)}
                    />,
                },
            ];
        },
        [
            apiData,
            apiOverwriteMode,
            folderId, folderOption,
            initialFolderOption,
            interfaceSign,
            projectOption,
            selectionState,
            teamIdAndProjectId,
            username,
        ]
    );

    const isDisableSave = useMemo(
        () => {
            return (selectedApis.length === 0 || isEmpty(teamIdAndProjectId) || !folderId);
        },
        [folderId, selectedApis.length, teamIdAndProjectId]
    );

    const disableText = useMemo(
        () => {
            if (isEmpty(teamIdAndProjectId)) {
                return '请选择项目后再进行保存';
            }
            if (!folderId) {
                return '请选择目录后再进行保存';
            }
            if (selectedApis.length === 0) {
                return '请选择至少一个接口后再进行保存';
            }
        },
        [folderId, selectedApis.length, teamIdAndProjectId]
    );

    return (
        <Spin size="large" spinning={loading}>
            <Container id="iapiContainer">
                <ContentArea>
                    <CustomizedCollapse
                        expandIcon={({isActive}) => <UpOutlined rotate={isActive ? 0 : 180} />}
                        expandIconPosition="end"
                        ghost
                        items={items}
                        defaultActiveKey={['1', '2']}
                        activeKey={activeKey}
                        onChange={setActiveKey}
                    />
                </ContentArea>
                <FooterBar>
                    {isDisableSave ? (
                        <Tooltip title={disableText}>
                            <StyledButton
                                disabled
                                type="primary"
                                onClick={handleSave}
                            >
                                保存
                            </StyledButton>
                        </Tooltip>
                    ) : (
                        <StyledButton
                            type="primary"
                            onClick={handleSave}
                        >
                            保存
                        </StyledButton>
                    )}
                </FooterBar>
            </Container>
        </Spin>
    );
};
