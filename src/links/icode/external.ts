/* eslint-disable max-lines */
import {createLink} from '@/utils/icode/link';
import {ICLOUD_HOST, WORK_LINK} from '@/constants/icode/urls';
import {APP_IS_ONLINE_PRODUCTION} from '@/constants/icode/app';
import {ParamsProcessId} from '@/types/icode/params';

export const UserNotFoundLink = createLink('https://cloud.baidu-int.com/icloud/iCode/常见问题/客户端问题/用户xxx账号不存在');

export const MergeFailedLink = createLink('https://cloud.baidu-int.com/icloud/iCode/常见问题/代码操作/合并失败');

export const GoodCoderLink = createLink('http://wenda.baidu.com/ask/question/56922');

export const RepoNameRuleLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码库管理/代码库命名规范介绍');

export const SpringRuleLink = createLink('https://cloud.baidu-int.com/icloud/iCode/代码治理/春季大扫除/');

export const PersonalRepoRuleLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码库管理/代码库命名规范介绍/#个人代码库');

export const BranchNameRuleLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码库管理/代码库命名规范介绍');

export const SSHKeyFailedLink = createLink('https://cloud.baidu-int.com/icloud/iCode/常见问题/代码库管理问题/添加SSHKEY失败');

export const ProductSettingLink = createLink('https://cloud.baidu-int.com/icloud/iCode/常见问题/代码库管理问题/当前目录没有管理员，怎么破');

export const ExemptLimitLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/NTxYlYHdtL/VkBZ0OMtiG0Baj');

export const ThirdPartyRuleLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/KdEstFV7vc/navimEkTNp4jPS');

export const DuplicateCheckLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/DS8eyHWPoh/aVGEF--8qV/M5FpabaqRZU5rq');

// 个人代码库说明
export const PersonalRepoGuideLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码库管理/代码库命名规范介绍');

// 资源账户说明
export const ICloudAccountGuideLink = createLink('https://cloud.baidu-int.com/icloud/help/企业组织/账户/如何创建百度智能云度厂版账户/');

// 资源账户申请创建说明
export const ICloudAccountAddLink = createLink('https://cloud.baidu-int.com/icloud/help/企业组织/账户/申请创建资源账户/');

// 资源账户更改说明
export const ICloudAccountEditLink = createLink('https://cloud.baidu-int.com/icloud/help/企业组织/资源查询&管理/资源移动/');

// 组织账户说明
export const ICloudOrganAccountGuideLink = createLink('https://cloud.baidu-int.com/icloud/help/企业组织/账户/如何创建云上百度账户/#12--什么时候需要新建组织账户：');

// 代码治理/学习代码复用
export const DuplicateHelpLink = createLink('https://cloud.baidu-int.com/icloud/iCode/代码治理/学习代码复用');

export const SshHelpLink = createLink('https://cloud.baidu-int.com/icloud/iCode/常见问题/客户端问题/SSH配置中的怎么做以及为什么');

// 个人中心数据计算规则
export const OwnerProcessLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码库管理/审核流程及规则');

export const AmendTipLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码管理/提交准入、并行多任务、线性历史');

export const ApplyExemptLink = createLink('https://cloud.baidu-int.com/icloud/iCode/百度编码规范/编码规范的豁免机制/编码规范实施及豁免FAQ');

// Qualified coder规则说明
export const QualifiedCoderLink = createLink('https://cloud.baidu-int.com/icloud/iCode/百度程序员的认证/QualifiedCoder认证制度');


export const SecurityCheckLink = createLink('https://cloud.baidu-int.com/icloud/iCode/开源软件/百度内部允许和禁止使用的第三方开源软件');

// 代码库语言审核规范
export const RepoLanguageApprovalLink = createLink('https://cloud.baidu-int.com/icloud/iCode/代码治理/代码库语言审核规范/');

interface ParamsSecurityRecordLink {
    repo: string;
    changeId: string;
    patchSetId: string | number;
}

export const SecurityRecordLink = createLink<ParamsSecurityRecordLink>('http://security.baidu.com/myflaw/web/#/report/apply');

// 代码搜索高级语法介绍
export const AdvancedSearchGuideLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/2376X9HPoGK_ah');

// 保护分支使用说明
export const ProtectedBranchHelpLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码库管理/保护分支功能');

// 消息通知管理使用说明
export const NoticeManagementHelpLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/个人设置/消息通知管理');

// Eslint 升级说明
export const EslintMigrateLink = createLink('https://cloud.baidu-int.com/icloud/iCode/百度编码规范/前端编码规范升级公告/');

export const RepoLimitLink = createLink('https://cloud.baidu-int.com/icloud/iCode/代码治理/代码不落盘(待定)');

export const PipelineSettingLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码库管理/限制流水线功能/');

// 百度流程中心
export const BaiduWorkLink = createLink('http://work.baidu.com');

export const CovLink = createLink('http://cov.baidu.com/');

export const SaLink = createLink('http://wbox.baidu-int.com/#/recall/sa');

export const IPipeLink = createLink(`${ICLOUD_HOST}/devops/ipipe`);

interface ParamsEmail {
    subject: string;
    // 其他参数暂无使用
}

// 代码搜索团队邮件
export const IcodeSearchTeamMailLink = createLink<ParamsEmail>('mailto:<EMAIL>');

// icode-pm邮件
export const IcodePmMailLink = createLink<ParamsEmail>('mailto:<EMAIL>');

export const ICodeSupportLink = createLink('baidu://message?appid=8elunc_XzfHLx7p3Fvl_0A');

// 代码库设置，代码库负责人（Owner）设置问题

export const RepoOwnerSupportLink = createLink('https://cloud.baidu-int.com/icloud/iCode/常见问题/代码库管理问题/代码库负责人（Owner）设置问题/');

// 个人中心，我发起的流程
export const ICloudInitiateLink = createLink(`${ICLOUD_HOST}/profile/initiate`);

export const ICodingCovUTLink = createLink(
    'https://cloud.baidu-int.com/icloud/iCode/云IDE/使用手册/云IDE支持UT在线调试使用手册'
);

export const MarkdownGuideLink = createLink('https://guides.github.com/features/mastering-markdown');

interface ParamsWorkspace {
    workspaceId: number;
}

export const IPipeRepoLink = createLink<ParamsWorkspace>(
    `${ICLOUD_HOST}/devops/ipipe/workspaces/{workspaceId}/pipelines/list`
);

interface ParamsIPipeBranchCreate {
    workspaceId: number;
    base?: string;
    title?: string;
    issueId?: string;
}

export const IPipeBranchCreateLink = createLink<ParamsIPipeBranchCreate>(
    `${ICLOUD_HOST}/devops/ipipe/workspaces/{workspaceId}/branches/add`
);

export const IRegistryLink = createLink(`${ICLOUD_HOST}/devops/iregistry`);

export const BaiduMailLink = createLink('http://mailadmin.baidu.com/index.do');

export const JsDepsSpecLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/ubCNglk-2u/unz1zRVe2RC6X5');

export const NoticeDocLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/个人设置/消息通知管理/');

// CNAP前端一站式平台简介
export const CNAPLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/FiCwh5KxY5/_d3c2Ac0_y8zJk');

// 编码规范实施及豁免FAQ
export const CodeScanExemptFaqLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/NTxYlYHdtL/6rCXKF3jo_bLvq');

// 语言治理及豁免FAQ
export const IdepsScanExemptFaqLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/xS56lc8VW5/oGpLigLjOF_31p');
export const IdepsFaqLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/xS56lc8VW5/ea6b44bdc6f040');

// API注释规范治理豁免FAQ
export const ApiGovExemptFaqLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/m2l2hEPGZF/AWe1SYWNi8LXXd');

interface ParamRuliuContactLink {
    ruliuId: string;
}

// 调起如流客户端与通信人对话
export const RuliuContactLink = createLink<ParamRuliuContactLink>('baidu://message/?id={ruliuId}');

// iCafe Cli 首页
export const ICafeCliHomeLink = createLink('https://icafe-cli.now.baidu-int.com');

// 值班表
export const DutyRosterLink = createLink('http://zhiban.baidu.com/duty/get?id=5d3670ea7eaeb018eed2dfe4');

// 文心一言-厂内业务iCafe空间
export const ICafeYiyanLink = createLink('https://console.cloud.baidu-int.com/devops/icafe/space/aiip-req/planbox/935483/issue?viewId=138905');

// 审批详情
export const ApprovalLink = createLink('https://console.cloud.baidu-int.com/profile/initiate');

const clientId = APP_IS_ONLINE_PRODUCTION ? 'Iv23li1n6vkN8OTsVIuj' : 'Ov23libabcHTsqhS6nSu';

const redirectUri = `${ICLOUD_HOST}/api/icode/rest/profile/opensource/bind`;

export const GithubOAuthLink = createLink(
    `https://github.com/login/oauth/authorize?client_id=${clientId}&redirect_uri=${redirectUri}`
);

export const TaskIdDocLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/IBJkjEp1-x/aYVeVzpwdJ/jRcxp3k8IaLBte');

export const DataReflowLink = createLink(`${ICLOUD_HOST}/comatestack/reflow`);

export const DepartmentComateDocLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/N9fWudlAxOilqb');

export const DepartmentStackDocLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/B8wSneaLSC/b80JnltTupcZGn');

export const BCloudHelpPageLink = createLink('http://buildcloud.baidu.com/?ht_kb=man-ci-yml');

export const NameStandardLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码库管理/代码库命名规范介绍');

export const StatisticsRuleLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/个人设置/代码库统计页面-规则说明');

export const LandingPageInstructionLink = createLink('https://cloud.baidu-int.com/icloud/iCode/%E5%BF%AB%E9%80%9F%E5%85%A5%E9%97%A8');

export const LandingPageDatasetLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/FCOIJjRkN7yMT7');

export const ProcessWorkLink = createLink<{processId: string}>(`${WORK_LINK}/{processId}`);

// eslint-disable-next-line max-len
export const ConsoleApprovalPageLink = createLink<ParamsProcessId>(`${ICLOUD_HOST}/profile/approval/moveDetails?processId={processId}&backType=initiate`);

// eslint-disable-next-line max-len
export const UserGroupsDisableLink = createLink('https://cloud.baidu-int.com/icloud/iCode/代码治理/用户组禁用');

// eslint-disable-next-line max-len
export const ApprovalPageLink = createLink<ParamsProcessId>(`${ICLOUD_HOST}/profile/approval/moveDetails?processId={processId}&backType=initiate`);

// 在线客服
export const OnlineServiceLink = createLink('baidu://message?appid=8elunc_XzfHLx7p3Fvl_0A');

// 帮助中心
export const HelpCenterLink = createLink('https://cloud.baidu-int.com/icloud/iCode/产品描述/产品概述');

// 常见问题
export const CommonQuestionLink = createLink('https://cloud.baidu-int.com/icloud/iCode/常见问题/代码库管理问题/为什么git只让建三级库名？/');

interface ParamsOkrWeekly {
    date: string;
}

// 团队视图介绍链接
export const TeamCenterLink = createLink('https://dwz.cn/BBnAtqbs');

// 个人视图介绍链接
export const PersonalCenterLink = createLink('https://dwz.cn/oRjbv23b');

// OKR周报
export const OkrWeeklyLink = createLink<ParamsOkrWeekly>('https://okr.baidu-int.com/pages/weekly.html#/home?date=s{date}&weekEntry=s&mandatorId=s&isEditAgain=false&rowId=null');

// 数据预警
export const DataWarningLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/个人设置/数据预警使用说明/');

// comate生成单测介绍链接
export const ComateUTLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/3rUJEdMDh-kv8j');

export const Contribution3DLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/BUNuKGY14l/m_EQl8HOwX5rfc');

export const ConfigurePipelineLink = createLink('https://cloud.baidu-int.com/icloud/ipipe/快速入门/流水线简介');

export const AmendHelpLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/QeXy5QCv8Hy8Nq##iCode提交流程最佳实践-用amend删除评审不通过的commit');

export const HelpLink = createLink('https://cloud.baidu-int.com/icloud/iCode/快速入门');

export const ReverseLink = createLink('http://buildcloud.baidu.com/stable/3-stable_dev_module#what_is_stable_reverse_build');

export const ReverseSolutionLink = createLink('http://buildcloud.baidu.com/stable/3-stable_dev_module#how_to_fix_when_reverse_build_failed');

interface ParamsUtTaskLink {
    task: string;
}

export const UtTaskLink = createLink<ParamsUtTaskLink>('http://cov.baidu.com/task?task={task}');

export const AutoFixCodeLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/DRENEnEC0FsfZo');

export const AutoCompleteNoteLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/42lMpXtzQVTvA6');

export const AutoGenerateUtCaseLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/3rUJEdMDh-kv8j');

export const UTGenerateHelpLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/s9pCbwNI04hEjA');

export const DeleteStableIssueLink = createLink('http://buildcloud.baidu.com/stable/4-stable_requirement#4.4-代码库设置说明');

export const ICodeOperatingInstructionLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码管理/在线回滚/');

export const MergeReferLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/代码管理/关于合入策略的说明');

// agile流水线链接
export const AgilePipelineLink = createLink('https://cloud.baidu-int.com/icloud/ipipe/快速入门/流水线简介');

// CI 图解
export const CIHelpLink = createLink('https://icloud-static.bj.bcebos.com/icode/frontend-v2/stable/assets/icode-process.gif');

export const SecurityHelpLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/KdEstFV7vc/59ebqGHtsoEqxr');

export const SecurityStyleHelpLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/3LKWVtUk1q/k41y6_B25GH6IC');

export const LanguageCheckHelpLink = createLink('https://cloud.baidu-int.com/icloud/iCode/代码治理/研发技术栈收敛/');

// 豁免方法的链接
export const ExemptionsHelpLink = createLink('https://cloud.baidu-int.com/icloud/iCode/百度编码规范/编码规范的豁免机制/编码规范实施及豁免FAQ');

// 代码可维护性指数(MI)检查 帮助链接
export const BugbyeLink = createLink('http://bugbye.baidu.com');

export const MavenLink = createLink('http://hetu.baidu.com/api/platform/index?platformId=2166');

export const PipLink = createLink('http://hetu.baidu.com/api/platform/index?platformId=2165');

export const ComposerLink = createLink('http://hetu.baidu.com/api/platform/index?platformId=1788');

export const NPMLink = createLink('http://npm.baidu-int.com');

export const BuildCloudHelpLink = createLink('http://buildcloud.baidu.com/?ht_kb=man-bcloud');

export const GithubMirrorLink = createLink('https://cloud.baidu-int.com/icloud/iCode/开源软件/GithubMirror');

export const GoLink = createLink('https://cloud.baidu-int.com/icloud/iCode/代码治理/百度内GoModule使用指南');

export const GoodCoderDocPageLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/NTxYlYHdtL/NHXnnI7nbo5b09');

export const WorkFlowLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/zANIpQEEV5/7sh2YSDfuTmIeM');

export const MoreWorkFlowLink = createLink('https://cloud.baidu-int.com/icloud/iCode/操作指南/分支管理/工作流');


export const OpenSourceLink = createLink('https://cloud.baidu-int.com/icloud/iCode/%E5%BC%80%E6%BA%90%E8%BD%AF%E4%BB%B6/%E5%BC%80%E6%BA%90%E5%8D%8F%E8%AE%AE%E5%90%88%E8%A7%84%E6%A3%80%E6%9F%A5');

// sourcegraph高级语法介绍文档
export const SourceGraphSearchInstructionLink = createLink('https://cloud.baidu-int.com/icloud/iCode/%E4%BB%A3%E7%A0%81%E6%90%9C%E7%B4%A2/%E9%AB%98%E7%BA%A7%E6%90%9C%E7%B4%A2%E8%AF%AD%E6%B3%95%E8%AF%B4%E6%98%8E');

// 首页搜索教学视频
export const searchVideoLink = 'https://bj.bcebos.com/v1/rte-file/cd8dab5d29a85a2b8c6cdc3b6a8b00dc?responseContentType=video%2Fmp4&responseContentDisposition=attachment%3B%20filename%3D%25E4%25BB%25A3%25E7%25A0%2581%25E6%2590%259C%25E7%25B4%25A2%25E9%25A6%2596%25E9%25A1%25B5%25E8%25A7%2586%25E9%25A2%2591.mp4&authorization=bce-auth-v1%2Ffbe74140929444858491fbf2b6bc0935%2F2022-08-05T08%3A37%3A08Z%2F604800%2F%2F30cd1959dcd3d1af4a3c23f61781350b1e20006f78308dcfa58b3e79f6f1a1a0';

// 智能搜索帮助文档
export const IntelligentSearchLink = createLink('https://cloud.baidu-int.com/icloud/iCode/%E4%BB%A3%E7%A0%81%E6%90%9C%E7%B4%A2/%E6%99%BA%E8%83%BD%E6%90%9C%E7%B4%A2%E8%AF%B4%E6%98%8E');

export const ComateCovUtGenerateDocLink = createLink('https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/t7n0qKWNJW/7urCLykvXO/jySFu9xAH3lzv-');

export const MergeRequestDocLink = createLink('https://dwz.cn/FhXCI0vq');
