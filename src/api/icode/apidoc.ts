import type {JSONSchema7} from 'json-schema';
import {createBApiInterface} from '@/utils/icode/api/createBApiInterface';

export const ResponseContentTypeMap = {
    text: {
        label: 'TEXT',
        value: 'text',
    },
    json: {
        label: 'JSO<PERSON>',
        value: 'json',
    },
    xml: {
        label: 'XML',
        value: 'xml',
    },
    html: {
        label: 'HTML',
        value: 'html',
    },
    raw: {
        label: 'Raw',
        value: 'raw',
    },
    binary: {
        label: 'Binary',
        value: 'binary',
    },
    msgpack: {
        label: 'MsgPack',
        value: 'msgpack',
    },
    eventStream: {
        label: 'Event-Stream',
        value: 'eventStream',
    },
};

export interface ParamsProxy {
    headers?: Header;
    payload?: CrPayload | WorkbenchPayload | TaskPayload[];
    params: Params;
    // 更新接口唯一标识的根目录id
    folderId?: string;
    // 项目id
    projectId?: string;
}

interface Header {
    'x-baidu-username'?: string;
    'x-project-id'?: string;
    'x-client-version'?: string;
    'Content-type'?: string;
}

// cr场景的payload
interface CrPayload {
    changeNumber?: string;
    commitId?: string;
    projectId?: number;
    name?: string;
    parentId?: string;
    swaggerIds?: string[];
    folderId?: number;
}

// 工作台场景的payload
interface WorkbenchPayload {
    // 更新接口唯一标识的根目录id
    id?: string;
    // 更新接口唯一标识的类型信息
    identityPattern?: {
        httpApi: {
            type: string;
            bodyType: string;
            fields: string[];
        };
    };
    // swagger数据
    data?: string;
    importFormat?: string;
    source?: string;
    apiOverwriteMode?: string;
    schemaOverwriteMode?: string;
    apiFolderId?: number;
    schemaFolderId?: number;
    importApiCase?: boolean;
    testCaseFolderId?: number;
    responseFolderId?: number;
    isNotified: boolean;
    dataImportResult?: ImportResult;
}

// 确认任务payload
interface TaskPayload {
    taskId: number;
    apiParseId: number;
    accepted: boolean;
    iapiName: string;
    iapiMethod: string;
    iapiId: string;
    iapiFolderId: string;
    iapiProjectId: string;
}

interface Params {
    sysToken?: string;
    commitId?: string;
    repoName?: string;
    changeNumber?: string;
    branchName?: string;
    username?: string;
    url?: string;
    taskId?: number;
    workId?: number;
    savedByUser?: string;
    source?: string;
}

export interface DirectoryResponseData {
    id: number;
    name: string;
    type: string;
    projectId: number;
    parentId: number;
    children: any[];
    serverId: string;
    preProcessors: any[];
    postProcessors: any[];
    inheritPreProcessors: Record<string, any>;
    inheritPostProcessors: Record<string, any>;
    auth: Record<string, any>;
    description: string;
    ordering: number;
    identityPattern: {
        httpApi: {
            type: string;
            bodyType: string;
            fields: any[];
        };
    };
    createdAt: string;
    updatedAt: string;
    creatorId: number;
    editorId: number;
    docId: number;
    projectBranchId: number;
}

export const createNewDirectory = createBApiInterface<ParamsProxy, DirectoryResponseData>(
    'POST',
    '/bapi/proxy/http/post?platform=iapi&api=/api/v1/api-detail-folders'
);

export const updateDirectory = createBApiInterface<ParamsProxy, DirectoryResponseData>(
    'POST',
    '/bapi/proxy/http/put?platform=iapi&api=/api/v1/api-detail-folders/{folderId}'
);

export const getProjectDirectories = createBApiInterface<ParamsProxy, DirectoryResponseData[]>(
    'POST',
    '/bapi/proxy/http/get?platform=iapi&api=/api/v1/api-detail-folders'
);

export interface ApiInfoResponseData {
    stage: number;
    isExistApi: boolean;
    apiDocGeneratedCount: number;
}

export const getApiInfo = createBApiInterface<ParamsProxy, ApiInfoResponseData|null>(
    'POST',
    '/bapi/proxy/http/get?platform=apidoc&api=/apidoc/crParse/queryById'
);

export enum ParameterDataTypeEnum {
    Number = 'number',
    String = 'string',
    Integer = 'integer',
    Array = 'array',
    File = 'file',
    // 仅1.4.14前版本可能会有text类型，新版本使用string代替text，旧版本text类型会客户端自动转换成string
    Text = 'text',
}

export interface IHttpApiParameter {
    id: string;
    name: string;
    required: boolean;
    description: string;
    type?: ParameterDataTypeEnum;
    // false 运行时不启用 true | undefined 运行时启用
    enable?: boolean;
    example?: string[] | string;
    fixedValue?: boolean;
    defaultEnable?: boolean;
    isModify?: boolean;
    isIdentityPattern?: boolean;
    contentType?: string;
}
export interface IHttpApiParameters {
    query?: IHttpApiParameter[];
    path?: IHttpApiParameter[];
    cookie?: IHttpApiParameter[];
    header?: IHttpApiParameter[];
}
export interface ApiResponseDefinition {
    name: string;
    code: number;
    contentType: string;
    jsonSchema: JSONSchema7;
    [property: string]: any;
}
export enum HttpApiRequestBodyType {
    None = 'none',
    XWwwFormUrlencoded = 'application/x-www-form-urlencoded',
    FormData = 'multipart/form-data',
    Json = 'application/json',
    Xml = 'application/xml',
    Raw = 'text/plain',
    Binary = 'application/octet-stream',
    GraphQL = 'graphql',
    MsgPack = 'application/x-msgpack',
  }
export interface ResponseExample {
    id?: number;
    name: string;
    data: string;
    responseId?: string | number;
    tempId?: string | number;
    ordering?: number;
}
export interface ApiCase {
    type: string;
    name: string;
    parameters: IHttpApiParameters;
    requestBody: {
      parameters: unknown[];
      data: string;
      type: string;
      generateMode: string;
    };
    responseId: string;
}
export interface Definition {
    type: string;
    properties: Record<string, PropertyDefinition>;
    required?: string[];
    name?: string;
    title?: string;
}

interface PropertyDefinition {
    type: string;
    description?: string;
    example?: string;
    format?: string;
    enum?: string[];
    additionalProperties?: PropertyDefinition;
    ref?: string;
    items?: {
      ref: string;
    };
}
export interface RequestBody {
    type: HttpApiRequestBodyType;
    parameters?: IHttpApiParameter[];
    jsonSchema?: JSONSchema7;
    data?: string;
    description?: string;
    /** @deprecated */
    sampleValue?: string;
    example?: string;
    graphql?: {
      query: string;
      variables: string;
    };
}
export interface ApiList {
    id: string;
    httpApi: {
      name: string;
      description: string;
      tags: string[];
      path: string;
      method: string;
      parameters: IHttpApiParameters;
      commonParameters: Record<string, unknown>;
      responses: ApiResponseDefinition[];
      responseExamples: ResponseExample[];
      requestBody: RequestBody;
      cases: ApiCase[];
      customApiFields: string;
      [property: string]: any;
    };
    definitions: Record<string, Definition>;
}

export interface ParamsGetApiDoc {
    changeNumber: string;
    commitId?: string;
}

interface getApiResponse {
    apiList: ApiList[];
}

export interface ApiListInfo {
    apiMethod: string;
    apiName: string;
    swaggerId: string;
}

export const getApiDoc = createBApiInterface<ParamsProxy, getApiResponse>(
    'POST',
    '/bapi/proxy/http/post?platform=iapi&api=/api/v1/projects/get-import-data'
);

export const getApiListById = createBApiInterface<ParamsProxy, ApiListInfo[]>(
    'POST',
    '/bapi/proxy/http/get?platform=apidoc&api=/apidoc/parseSwagger/query/apiListById'
);

export const getMergedApiDoc = createBApiInterface<ParamsProxy, getApiResponse>(
    'POST',
    '/bapi/proxy/http/post?platform=iapi&api=/api/v1/projects/pre-import-data'
);

export interface GetTeamAndProjectResponse {
    defaultDisplay: Team[];
    teams: Team[];
}

export interface Team {
    collaboratedAt?: null;
    createdAt?: string;
    deletedAt?: null;
    description?: string;
    id?: number;
    name?: string;
    ownerId?: number;
    project?: TeamProject[];
    resourceLimitExceed?: number;
    updatedAt?: string;
    children?: TeamProject[];
}

export interface TeamProject {
    _icon?: string;
    createdAt?: string;
    creatorId?: number;
    deletedAt?: null;
    description?: string;
    editorId?: number;
    id?: number;
    isMaster?: boolean;
    mockRule?: {
        enableSystemRule: boolean;
        rules?: string[];
    };
    name?: string;
    ordering?: number;
    robotNotice?: string;
    strictMode?: number;
    teamId?: number;
    type?: string;
    updatedAt?: string;
    visibility?: string;
}

export const getTeamAndProject = createBApiInterface<ParamsProxy, GetTeamAndProjectResponse>(
    'POST',
    '/bapi/proxy/http/get?platform=iapi&api=/api/v1/iapi/user-teams-projects/importable'
);

export interface ParamsSaveApiDoc {
    projectId: string;
    swaggerIds: string[];
    folderId: number;
}

export interface SaveApiDocResponse {
    apiList: Array<{
        id: number;
        [property: string]: any;
    }>;
}

export const saveApiDoc = createBApiInterface<ParamsProxy, SaveApiDocResponse>(
    'POST',
    '/bapi/proxy/http/post?platform=iapi&api=/api/v1/projects/batch-import-data'
);

interface ParamsSaveUserConfigs {
    branch: string;
    directoryId: number;
    projectId: number;
    repoName: string;
    teamId: number;
    username: string;
}

export const saveUserConfigs = createBApiInterface<ParamsSaveUserConfigs, boolean>(
    'POST',
    '/bapi/userConfig/save'
);

interface ParamsGetUserConfigs {
    branch?: string;
    repoName?: string;
    username?: string;
}

export interface UserConfigs {
    directoryId: number;
    projectId: number;
    teamId: number;
}

export const getUserConfigs = createBApiInterface<ParamsGetUserConfigs, UserConfigs>(
    'GET',
    '/bapi/userConfig/queryByCommitMsg'
);

interface ParamsGetGrayPermission {
    username: string;
    eventCode: string;
}

export const getGrayPermission = createBApiInterface<ParamsGetGrayPermission, boolean>(
    'GET',
    '/bapi/iapi/userAuth/grayEventPermission/userEvent'
);

export interface ApiDocByTaskId {
    /**
     * 生成的接口唯一id
     */
    apiParseId?: number;
    createdTime?: string;
    /**
     * 接口id，当status=ACCEPTED时才有
     */
    content?: string;
    apiName?: string;
    apiMethod?: string;
    apiPath?: string;
    catalog?: string;
    iapiId?: string;
    /**
     * 拼接后的接口url，当status=ACCEPTED时才有
     */
    iapiUrl?: string;
    /**
     * 保存人，当status=ACCEPTED时才有
     */
    savedByUser?: string;
    /**
     * 状态，采纳为status=ACCEPTED
     */
    status?: string;
    /**
     * swagger内容
     */
    swaggerContent?: string;
    /**
     * 任务
     */
    taskId?: number;
    /**
     * 保存时间
     */
    updatedTime?: string;
}

export interface HttpApiInfo {
    _databaseId: number;
    _parameters: string;
    _requestBody: string;
    _status: string;
    advancedSettings: { [key: string]: any };
    auth: { [key: string]: any };
    commonParameters: { [key: string]: any };
    commonResponseStatus: { [key: string]: any };
    createdAt: string;
    createdByActor: null;
    creatorId: number;
    customApiFields: { [key: string]: any };
    description: string;
    editorId: number;
    folderId: number;
    id: number;
    inheritPostProcessors: { [key: string]: any };
    inheritPreProcessors: { [key: string]: any };
    method: string;
    mockScript: { [key: string]: any };
    name: string;
    operationId: string;
    ordering: number;
    path: string;
    postProcessors: string[];
    preProcessors: string[];
    projectId: number;
    responseChildren: string[];
    responsibleId: number;
    serverId: string;
    source: string;
    sourceUrl: string;
    tags: string[];
    type: string;
    updatedAt: string;
    updatedByActor: null;
    [property: string]: any;
}

export interface ImportResponse {
    httpApiInfo: HttpApiInfo;
    apiCollection: ApiCollection;
    docCollection: DocCollection;
    environment: Environment;
    responseCollection: ResponseCollection;
    schemaCollection: SchemaCollection;
    socketCollection: SocketCollection;
    testCaseCollection: TestCaseCollection;
    testSuiteCollection: TestSuiteCollection;
    webSocketCollection: WebSocketCollection;
}

export interface UserInfo {
    avatar: string;
    createdAt: string;
    updatedAt: string;
    email: string;
    id: number;
    name: string;
    mobile: string;
    username: string;
}

export const getUserInfo = createBApiInterface<ParamsProxy, UserInfo[]>(
    'POST',
    '/bapi/proxy/http/get?platform=iapi&api=/api/v1/get-user-info'
);

export const createNewTeamAndProject = createBApiInterface<ParamsProxy, TeamProject>(
    'POST',
    '/bapi/proxy/http/post?platform=iapi&api=/api/v1/create/user-team-project'
);

export const getImportableProjects = createBApiInterface<ParamsProxy, Team[]>(
    'POST',
    '/bapi/proxy/http/get?platform=iapi&api=/api/v1/get-teams-projects/import'
);

export interface MetaData {
    projectId: string;
    teamId: string;
}

export const getMetaData = createBApiInterface<ParamsProxy, MetaData>(
    'POST',
    '/bapi/proxy/http/get?platform=apidoc&api=/api/parse/result/metaData'
);

export const getApiDocByTaskId = createBApiInterface<ParamsProxy, ApiDocByTaskId[]>(
    'POST',
    '/bapi/proxy/http/get?platform=apidoc&api=/api/parse/task/query'
);

export const confirmByTaskId = createBApiInterface<ParamsProxy, null>(
    'POST',
    '/bapi/proxy/http/post?platform=apidoc&api=/api/parse/task/confirm'
);

export const getApiDocByWorkId = createBApiInterface<ParamsProxy, ApiDocByTaskId[]>(
    'POST',
    '/bapi/proxy/http/get?platform=apidoc&api=/api/parse/result/query'
);

export const confirmByWorkId = createBApiInterface<ParamsProxy, null>(
    'POST',
    '/bapi/proxy/http/post?platform=apidoc&api=/api/parse/result/confirm'
);

export const importApiToProject = createBApiInterface<ParamsProxy, ImportResponse>(
    'POST',
    '/bapi/proxy/http/post?platform=iapi&api=/api/v1/projects/{projectId}/import-data'
);

export interface ImportResult {
    apiCollection: ApiCollection;
    docCollection: DocCollection;
    environment: Environment;
    responseCollection: ResponseCollection;
    schemaCollection: SchemaCollection;
    socketCollection: SocketCollection;
    testCaseCollection: TestCaseCollection;
    testSuiteCollection: TestSuiteCollection;
    webSocketCollection: WebSocketCollection;
    [property: string]: any;
  }

export interface ApiCollection {
    folder: ApiCollectionFolder;
    item: ApiCollectionItem;
  }

export interface ApiCollectionFolder {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface ApiCollectionItem {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface DocCollection {
    folder: DocCollectionFolder;
    item: DocCollectionItem;
  }

export interface DocCollectionFolder {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface DocCollectionItem {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface Environment {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface ResponseCollection {
    folder: ResponseCollectionFolder;
    item: ResponseCollectionItem;
  }

export interface ResponseCollectionFolder {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface ResponseCollectionItem {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface SchemaCollection {
    folder: SchemaCollectionFolder;
    item: SchemaCollectionItem;
  }

export interface SchemaCollectionFolder {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface SchemaCollectionItem {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface SocketCollection {
    api: Api;
    folder: SocketCollectionFolder;
    service: Service;
  }

export interface Api {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface SocketCollectionFolder {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface Service {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface TestCaseCollection {
    folder: TestCaseCollectionFolder;
    item: TestCaseCollectionItem;
  }

export interface TestCaseCollectionFolder {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface TestCaseCollectionItem {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface TestSuiteCollection {
    folder: TestSuiteCollectionFolder;
    item: TestSuiteCollectionItem;
  }

export interface TestSuiteCollectionFolder {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface TestSuiteCollectionItem {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface WebSocketCollection {
    folder: WebSocketCollectionFolder;
    item: WebSocketCollectionItem;
  }

export interface WebSocketCollectionFolder {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }

export interface WebSocketCollectionItem {
    createCount: number;
    errorCount: number;
    errorIds: number[];
    ignoreCount: number;
    updateCount: number;
  }
